# Enable git prompt
autoload -Uz vcs_info
precmd() { vcs_info }
zstyle ':vcs_info:git:*' formats ' (%b)'
setopt PROMPT_SUBST

# Custom prompt with git branch
PROMPT='💩🎉 %F{cyan}%n@%m%f:%F{yellow}%~%f%F{green}${vcs_info_msg_0_}%f %F{magenta}$%f '

# Enable command completion
autoload -U compinit
compinit

# History configuration
HISTFILE="$ZDOTDIR/.zsh_history"
HISTSIZE=10000
SAVEHIST=10000
setopt SHARE_HISTORY
setopt HIST_IGNORE_DUPS
setopt HIST_IGNORE_ALL_DUPS

# Enable useful zsh features
setopt AUTO_CD
setopt CORRECT
setopt EXTENDED_GLOB

# Aliases for TurdParty development
alias ll='eza -la --git'
alias la='eza -la'
alias ls='eza'
alias cat='bat'
alias grep='rg'
alias find='fd'
alias ps='ps aux'
alias df='df -h'
alias du='du -h'
alias free='free -h'

# Git aliases
alias gs='git status'
alias ga='git add'
alias gc='git commit'
alias gp='git push'
alias gl='git log --oneline'
alias gb='git branch'
alias gco='git checkout'

# TurdParty specific aliases
alias tp-status='./scripts/turdparty status'
alias tp-monitor='./scripts/turdparty monitor'
alias tp-start='./scripts/turdparty start'
alias tp-test='./scripts/run-parallel-tests.sh'
alias tp-logs='docker-compose logs -f'

# Docker aliases
alias dc='docker-compose'
alias dcu='docker-compose up -d'
alias dcd='docker-compose down'
alias dcl='docker-compose logs -f'
alias dps='docker ps'
alias di='docker images'

echo "🎉 Zsh configured with git branch display and TurdParty aliases!"
