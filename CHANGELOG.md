# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### 🎉 Recent Updates

#### ✅ API Service and Project Cleanup
- **Fixed API service** - Resolved docker-compose configuration issues
- **Main directory cleanup** - Moved 14 closed PRD files to archive
- **Sphinx report generation** - Successfully generated documentation via API
- **Project organisation** - Cleaner main directory structure

#### 🏗️ System Architecture Updates
- **Enhanced architecture diagram** - Updated Mermaid diagram with current services
- **Improved documentation** - Better representation of data flow and components
- **Service integration** - Clearer visualization of Traefik, Celery, and ELK stack

### 🎉 Major Testing Framework Implementation

#### ✅ Modern Testing Stack Completed
- **World-class testing framework** with industry-standard tools
- **Zero warnings** - Clean, modern Python codebase
- **Comprehensive test coverage** across multiple testing paradigms
- **Performance optimised** - Fast execution and reliable results

#### 🧪 Test Suite Status

```mermaid
graph TB
    subgraph "🧪 TurdParty Testing Framework"
        A[Core Testing] --> B[Unit Tests: 12/12 ✅]
        A --> C[Property Tests: 9/9 ✅]
        A --> D[Performance Tests: 1/1 ✅]

        B --> B1[Basic Functionality]
        B --> B2[File Operations]
        B --> B3[Hash Calculations]
        B --> B4[Type Annotations]

        C --> C1[Model Validation]
        C --> C2[Data Integrity]
        C --> C3[Edge Case Discovery]

        D --> D1[Model Creation: 1.30M ops/s]

        E[Code Quality] --> F[Ruff: ✅ CLEAN]
        E --> G[MyPy: ✅ CONFIGURED]
        E --> H[Bandit: ✅ CONFIGURED]

        I[Documentation] --> J[Sphinx Docs: ✅ BUILT]
        I --> K[API Reference: ✅ READY]
        I --> L[Testing Guide: ✅ COMPLETE]
    end

    style A fill:#2980B9,stroke:#fff,stroke-width:2px,colour:#fff
    style B fill:#27AE60,stroke:#fff,stroke-width:2px,colour:#fff
    style C fill:#27AE60,stroke:#fff,stroke-width:2px,colour:#fff
    style D fill:#27AE60,stroke:#fff,stroke-width:2px,colour:#fff
    style E fill:#F39C12,stroke:#fff,stroke-width:2px,colour:#fff
    style I fill:#8E44AD,stroke:#fff,stroke-width:2px,colour:#fff
```

#### 📊 Test Results Summary

| Test Category | Count | Status | Performance | Coverage |
|---------------|-------|--------|-------------|----------|
| **Unit Tests** | 12/12 | ✅ PASSING | 0.37s | Core functionality |
| **Property Tests** | 9/9 | ✅ PASSING | 1.64s | Edge case discovery |
| **Performance Tests** | 1/1 | ✅ PASSING | 1.30M ops/s | Model creation |
| **Code Quality** | All | ✅ CLEAN | Fast | Zero warnings |

### Added
- **API Service Fixes**
  - Fixed docker-compose configuration for API service
  - API now accessible at http://localhost:8000/health/
  - Sphinx report generation via API endpoints working
  - Comprehensive error handling and service health checks

- **Project Organisation**
  - Archive directory for closed PRDs and implementation logs
  - Cleaner main directory with only active project files
  - Comprehensive archive documentation with migration notes
  - Updated .gitignore to exclude archive directory from tracking

- **Enhanced System Architecture**
  - Updated Mermaid diagram with current service topology
  - Better representation of Traefik routing and domain structure
  - Clearer visualization of Celery worker architecture
  - Improved ELK stack integration documentation

- **Modern Testing Framework**
  - pytest 8.3.3 with comprehensive plugin ecosystem
  - Hypothesis 6.112.2 for property-based testing
  - pytest-benchmark 4.0.0 for performance validation
  - Complete Nix development environment with Python 3.12.8

- **Core Test Suites**
  - 12 unit tests covering basic functionality, file operations, hash calculations
  - 9 property-based tests for automated edge case discovery
  - 1 performance benchmark achieving 1.30M operations/second
  - Zero warnings across all test runs

- **Development Infrastructure**
  - Nix shell with all dependencies properly configured
  - Ruff for fast linting and formatting (zero issues)
  - MyPy for static type checking
  - Bandit for security scanning
  - Pre-commit hooks for automated quality gates

- **Documentation System**
  - Comprehensive Sphinx documentation in tests/docs/
  - Complete testing framework guides and tutorials
  - API reference documentation
  - Interactive examples with copy-paste functionality

- **Project Organization**
  - Cleaned up main directory structure
  - Organised test files by category (unit, property, performance)
  - Comprehensive build and serve scripts for documentation

### Changed
- **System Architecture Documentation** - Updated diagrams to reflect current service topology
- **Main Directory Structure** - Moved completed PRDs to archive for better organisation
- **API Service Configuration** - Switched to correct docker-compose setup
- **Documentation Format** - Enhanced README with better architecture visualization
- **Testing Strategy** - Moved to modern, industry-standard tools
- **Development Environment** - Unified Nix-based setup with Python 3.12.8
- **Code Quality** - Achieved zero warnings across entire codebase

### Fixed
- **API Service Accessibility** - Resolved docker-compose configuration conflicts
- **Container Name Conflicts** - Fixed overlapping container names between compose files
- **Sphinx Report Generation** - API endpoints now properly generate documentation
- **Service Health Monitoring** - All services now report correct health status
- **Dependency Management** - All testing dependencies properly configured in shell.nix
- **Test Execution** - Fast, reliable test runs with comprehensive coverage
- **Code Quality** - Resolved all linting issues and warnings
- **Documentation Build** - Sphinx documentation builds successfully

### Security
- **Security Scanning** - Bandit configured for vulnerability detection
- **Code Quality** - Ruff ensures secure coding patterns

## [1.0.0]

### Added
- Celery integration for asynchronous task processing
  - Created task queues for file operations, VM lifecycle, VM injection, and monitoring
  - Implemented task status tracking and monitoring
  - Added Celery workers for each queue type
  - Added Redis as message broker and result backend
- Docker container namespacing with `turdparty_` prefix
  - Updated all Docker containers to use consistent naming convention
  - Created dedicated Docker networks for each environment
  - Updated scripts to work with the new container naming convention
- Comprehensive documentation
  - Added Docker namespacing documentation
  - Added Celery integration testing documentation
  - Updated existing documentation to reflect changes

### Changed
- Renamed all Docker containers from `dockerwrapper-*` to `turdparty_*`
- Updated Docker Compose files to use the new naming convention
- Updated scripts to use the new container names
- Improved testing infrastructure with Docker-based integration tests
- Enhanced README with updated information

### Fixed
- Fixed Celery integration tests
- Fixed database connection issues in Docker containers
- Fixed dependency conflicts with pydantic versions

### Removed
- Transition documentation (no longer needed)
- Old Docker container naming convention

## [0.9.0]

### Added
- Initial implementation of Celery task processing
- Basic Docker container setup
- File upload functionality
- VM management functionality
- File injection functionality

---

## Changelog Guidelines

### Types of Changes

- **Added** for new features
- **Changed** for changes in existing functionality
- **Deprecated** for soon-to-be removed features
- **Removed** for now removed features
- **Fixed** for any bug fixes
- **Security** in case of vulnerabilities

### Version Format

This project uses [Semantic Versioning](https://semver.org/):
- **MAJOR** version for incompatible API changes
- **MINOR** version for backwards-compatible functionality additions
- **PATCH** version for backwards-compatible bug fixes

### Entry Format

Each entry should:
- Use present tense ("Add feature" not "Added feature")
- Be concise but descriptive
- Include relevant context when helpful
- Group related changes together
- Use British English spelling and conventions
- **Omit release dates** - Dates are automatically added from git tags during release

### Example Entry

```markdown
## [1.2.0]

### Added
- User authentication system with JWT tokens
- Password reset functionality via email
- Role-based access control for admin features

### Changed
- Updated API response format for consistency
- Improved error handling across all endpoints

### Fixed
- Resolved memory leak in file upload process
- Fixed timezone handling in date calculations

### Security
- Updated dependencies to address security vulnerabilities
- Implemented rate limiting on authentication endpoints
```
