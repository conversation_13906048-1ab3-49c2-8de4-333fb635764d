input {
  # TCP input for direct application logs
  tcp {
    port => 5000
    codec => json_lines
    tags => ["tcp_logs"]
  }

  # Beats input for Filebeat (Docker container logs)
  beats {
    port => 5044
    tags => ["docker_logs"]
  }

  # File input for log files
  file {
    path => "/app/logs/*.log"
    start_position => "beginning"
    sincedb_path => "/dev/null"
    codec => "json"
    tags => ["file_logs"]
  }
}

filter {
  # Process Docker container logs from Filebeat
  if "docker_logs" in [tags] {
    # Parse Docker JSON log format
    if [message] {
      json {
        source => "message"
        target => "docker"
      }
    }

    # Extract container information and create ECS-compliant fields
    if [container][name] {
      mutate {
        add_field => { "service_name" => "%{[container][name]}" }
        add_field => { "container_id" => "%{[container][id]}" }
        add_field => { "container_image" => "%{[container][image][name]}" }
      }

      # Clean up service name (remove turdpartycollab_ prefix)
      mutate {
        gsub => [ "service_name", "turdpartycollab_", "" ]
      }

      # Create ECS service fields
      mutate {
        add_field => { "[service][name]" => "%{service_name}" }
        add_field => { "[service][type]" => "turdparty" }
        add_field => { "[service][version]" => "1.0.0" }
        add_field => { "[service][environment]" => "development" }
      }

      # Create ECS container fields
      mutate {
        add_field => { "[container][name]" => "%{[container][name]}" }
        add_field => { "[container][id]" => "%{container_id}" }
        add_field => { "[container][image][name]" => "%{container_image}" }
        add_field => { "[container][runtime]" => "docker" }
      }
    }

    # Use log field as main message for Docker logs
    if [log] {
      mutate {
        rename => { "log" => "message" }
      }
    }

    # Add comprehensive debugging context
    mutate {
      add_field => { "log_source" => "docker" }
      add_field => { "platform" => "turdparty" }
      add_field => { "[labels][platform]" => "turdparty" }
      add_field => { "[labels][log_source]" => "docker" }
      add_field => { "[labels][collection_method]" => "filebeat" }
    }

    # Parse structured logs from TurdParty applications
    if [message] =~ /^\{.*\}$/ {
      json {
        source => "message"
        target => "app_log"
        add_tag => ["structured_log"]
      }

      # Extract structured log fields into ECS format
      if [app_log][timestamp] {
        mutate {
          add_field => { "[@timestamp]" => "%{[app_log][timestamp]}" }
        }
      }

      if [app_log][level] {
        mutate {
          add_field => { "[log][level]" => "%{[app_log][level]}" }
        }
      }

      if [app_log][service] {
        mutate {
          add_field => { "[service][name]" => "%{[app_log][service]}" }
        }
      }

      if [app_log][logger] {
        mutate {
          add_field => { "[log][logger]" => "%{[app_log][logger]}" }
        }
      }

      if [app_log][correlation_id] {
        mutate {
          add_field => { "[trace][id]" => "%{[app_log][correlation_id]}" }
          add_field => { "[labels][correlation_id]" => "%{[app_log][correlation_id]}" }
        }
      }

      if [app_log][module] {
        mutate {
          add_field => { "[labels][module]" => "%{[app_log][module]}" }
        }
      }

      if [app_log][function] {
        mutate {
          add_field => { "[labels][function]" => "%{[app_log][function]}" }
        }
      }

      if [app_log][line] {
        mutate {
          add_field => { "[labels][line_number]" => "%{[app_log][line]}" }
        }
      }

      # Use structured message if available
      if [app_log][message] {
        mutate {
          update => { "message" => "%{[app_log][message]}" }
        }
      }
    }
  }

  # Parse timestamp with multiple formats
  if [timestamp] {
    date {
      match => [ "timestamp", "ISO8601", "yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS'Z'", "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'" ]
      target => "@timestamp"
    }
  }

  # Parse @timestamp from Filebeat
  if [@timestamp] {
    date {
      match => [ "@timestamp", "ISO8601" ]
      target => "@timestamp"
    }
  }

  # Add service identification for non-Docker logs with ECS compliance
  if ![service_name] {
    mutate {
      add_field => { "service_name" => "turdparty" }
      add_field => { "[service][name]" => "turdparty" }
      add_field => { "[service][type]" => "application" }
    }
  }

  # Add ECS event fields based on log content
  mutate {
    add_field => { "[event][dataset]" => "turdparty.%{service_name}" }
    add_field => { "[event][module]" => "turdparty" }
    add_field => { "[event][created]" => "%{@timestamp}" }
  }

  # Determine event category and type based on service and content
  if [service_name] == "api" {
    mutate {
      add_field => { "[event][category]" => ["application"] }
      add_field => { "[event][type]" => ["info"] }
    }

    # WebSocket-specific categorization
    if [message] =~ /WebSocket/ {
      mutate {
        update => { "[event][category]" => ["network", "application"] }
        update => { "[event][type]" => ["connection", "protocol"] }
        add_field => { "[labels][protocol]" => "websocket" }
      }
    }

    # Route registration categorization
    if [message] =~ /(Registering|Registered|Failed to register)/ {
      mutate {
        update => { "[event][category]" => ["configuration", "application"] }
        update => { "[event][type]" => ["change", "info"] }
        add_field => { "[labels][operation]" => "route_registration" }
      }
    }
  } else if [service_name] == "database" {
    mutate {
      add_field => { "[event][category]" => ["database"] }
      add_field => { "[event][type]" => ["info"] }
    }
  } else if [service_name] == "elasticsearch" {
    mutate {
      add_field => { "[event][category]" => ["database"] }
      add_field => { "[event][type]" => ["info"] }
    }
  } else if [service_name] =~ /^(frontend|docs|status)$/ {
    mutate {
      add_field => { "[event][category]" => ["web"] }
      add_field => { "[event][type]" => ["access"] }
    }
  } else if [service_name] =~ /^(celery-worker|celery-beat|vm-monitor)$/ {
    mutate {
      add_field => { "[event][category]" => ["process"] }
      add_field => { "[event][type]" => ["info"] }
    }
  } else {
    mutate {
      add_field => { "[event][category]" => ["application"] }
      add_field => { "[event][type]" => ["info"] }
    }
  }

  # Add host information in ECS format
  mutate {
    add_field => { "[host][name]" => "%{[beat][hostname]}" }
    add_field => { "[host][hostname]" => "%{[beat][hostname]}" }
  }

  # Add comprehensive debugging labels
  mutate {
    add_field => { "[labels][environment]" => "development" }
    add_field => { "[labels][platform]" => "turdparty" }
    add_field => { "[labels][log_processor]" => "logstash" }
    add_field => { "[labels][ecs_version]" => "8.11.0" }
  }
  
  # Process file injection events
  if [event_type] == "injection_created" {
    mutate {
      add_field => { "event_category" => "file_injection" }
      add_field => { "event_action" => "create" }
    }
  }
  
  if [event_type] == "injection_processed" {
    mutate {
      add_field => { "event_category" => "file_injection" }
      add_field => { "event_action" => "process" }
    }
  }
  
  if [event_type] == "injection_failed" {
    mutate {
      add_field => { "event_category" => "file_injection" }
      add_field => { "event_action" => "error" }
      add_field => { "log_level" => "ERROR" }
    }
  }
  
  # Process installation base events
  if [event_type] == "installation_base" {
    mutate {
      add_field => { "event_category" => "installation" }
      add_field => { "event_action" => "base_log" }
    }
    
    # Extract installation details
    if [installation_data] {
      ruby {
        code => "
          installation_data = event.get('installation_data')
          if installation_data.is_a?(Hash)
            installation_data.each do |key, value|
              event.set('installation_' + key, value)
            end
          end
        "
      }
    }
  }
  
  # Add geolocation if IP is present
  if [client_ip] {
    geoip {
      source => "client_ip"
      target => "geoip"
    }
  }
  
  # Parse user agent if present
  if [user_agent] {
    useragent {
      source => "user_agent"
      target => "ua"
    }
  }
  
  # Clean up fields
  mutate {
    remove_field => [ "host", "path", "tags" ]
  }
}

output {
  # Output to Elasticsearch with ECS-compliant indexing
  elasticsearch {
    hosts => ["elasticsearch:9200"]

    # ECS-compliant dynamic indexing with service and date
    index => "ecs-turdparty-%{[service][name]:unknown}-%{+YYYY.MM.dd}"

    # Use different index patterns for different log types
    if "docker_logs" in [tags] {
      index => "ecs-turdparty-docker-%{[service][name]:unknown}-%{+YYYY.MM.dd}"
    }

    if "structured_log" in [tags] {
      index => "ecs-turdparty-app-%{[service][name]:unknown}-%{+YYYY.MM.dd}"
    }

    # ECS-compliant template configuration
    template_name => "ecs-turdparty"
    template_pattern => "ecs-turdparty-*"
    template => {
      "index_patterns" => ["ecs-turdparty-*"],
      "settings" => {
        "number_of_shards" => 1,
        "number_of_replicas" => 0,
        "index.refresh_interval" => "5s",
        "index.mapping.total_fields.limit" => 2000
      },
      "mappings" => {
        "properties" => {
          # ECS Core Fields
          "@timestamp" => { "type" => "date" },
          "timestamp" => { "type" => "date" },
          "message" => { "type" => "text", "analyzer" => "standard" },
          "tags" => { "type" => "keyword" },

          # ECS Event Fields
          "event" => {
            "properties" => {
              "category" => { "type" => "keyword" },
              "type" => { "type" => "keyword" },
              "action" => { "type" => "keyword" },
              "outcome" => { "type" => "keyword" },
              "dataset" => { "type" => "keyword" },
              "module" => { "type" => "keyword" },
              "created" => { "type" => "date" },
              "duration" => { "type" => "long" },
              "severity" => { "type" => "long" }
            }
          },

          # ECS Service Fields
          "service" => {
            "properties" => {
              "name" => { "type" => "keyword" },
              "type" => { "type" => "keyword" },
              "version" => { "type" => "keyword" },
              "environment" => { "type" => "keyword" },
              "state" => { "type" => "keyword" }
            }
          },

          # ECS Container Fields
          "container" => {
            "properties" => {
              "name" => { "type" => "keyword" },
              "id" => { "type" => "keyword" },
              "runtime" => { "type" => "keyword" },
              "image" => {
                "properties" => {
                  "name" => { "type" => "keyword" },
                  "tag" => { "type" => "keyword" },
                  "hash" => { "type" => "keyword" }
                }
              },
              "labels" => { "type" => "object" }
            }
          },

          # ECS Host Fields
          "host" => {
            "properties" => {
              "name" => { "type" => "keyword" },
              "hostname" => { "type" => "keyword" },
              "ip" => { "type" => "ip" },
              "mac" => { "type" => "keyword" },
              "architecture" => { "type" => "keyword" },
              "os" => {
                "properties" => {
                  "name" => { "type" => "keyword" },
                  "version" => { "type" => "keyword" },
                  "platform" => { "type" => "keyword" }
                }
              }
            }
          },

          # ECS Log Fields
          "log" => {
            "properties" => {
              "level" => { "type" => "keyword" },
              "logger" => { "type" => "keyword" },
              "origin" => {
                "properties" => {
                  "file" => {
                    "properties" => {
                      "name" => { "type" => "keyword" },
                      "line" => { "type" => "integer" }
                    }
                  },
                  "function" => { "type" => "keyword" }
                }
              }
            }
          },

          # ECS Trace Fields (for correlation)
          "trace" => {
            "properties" => {
              "id" => { "type" => "keyword" },
              "transaction" => {
                "properties" => {
                  "id" => { "type" => "keyword" }
                }
              }
            }
          },

          # ECS Labels (for debugging context)
          "labels" => {
            "properties" => {
              "platform" => { "type" => "keyword" },
              "environment" => { "type" => "keyword" },
              "log_source" => { "type" => "keyword" },
              "collection_method" => { "type" => "keyword" },
              "log_processor" => { "type" => "keyword" },
              "ecs_version" => { "type" => "keyword" },
              "correlation_id" => { "type" => "keyword" },
              "module" => { "type" => "keyword" },
              "function" => { "type" => "keyword" },
              "line_number" => { "type" => "integer" },
              "protocol" => { "type" => "keyword" },
              "operation" => { "type" => "keyword" }
            }
          },

          # TurdParty-specific fields
          "injection_id" => { "type" => "keyword" },
          "vm_id" => { "type" => "keyword" },
          "filename" => { "type" => "text" },
          "target_path" => { "type" => "text" },
          "status" => { "type" => "keyword" },
          "error_message" => { "type" => "text" },
          "details" => { "type" => "object" },
          "installation_data" => { "type" => "object" },

          # Legacy fields for backward compatibility
          "service_name" => { "type" => "keyword" },
          "event_type" => { "type" => "keyword" },
          "event_category" => { "type" => "keyword" },
          "event_action" => { "type" => "keyword" },
          "log_level" => { "type" => "keyword" },
          "log_source" => { "type" => "keyword" },
          "platform" => { "type" => "keyword" },
          "container_id" => { "type" => "keyword" },
          "container_image" => { "type" => "keyword" },

          # Application-specific structured log fields
          "app_log" => { "type" => "object" },
          "docker" => { "type" => "object" },

          # Geolocation fields
          "geoip" => {
            "properties" => {
              "location" => { "type" => "geo_point" },
              "country_name" => { "type" => "keyword" },
              "city_name" => { "type" => "keyword" }
            }
          }
        }
      }
    }
  }
  
  # Debug output to stdout (can be removed in production)
  stdout {
    codec => rubydebug
  }
}
