
7Zip Analysis Report
====================

Binary Information
------------------


Execution Summary
-----------------


Installation Footprint
-----------------------



Runtime Behavior
-----------------


Security Analysis
-----------------



ECS Data Collection
-------------------



Evidence Box
------------

**Direct Data Access:**

- `Elasticsearch Query <http://localhost:9200/turdparty-rich-cli-ecs-*/_search?q=turdparty.binary_name:7zip>`_
- `Kibana Dashboard <http://localhost:5601/app/discover#/?_g=(filters:!(),time:(from:now-1h,to:now))&_a=(query:(match:(turdparty.binary_name:7zip)))>`_

**API Endpoints:**

- `Full Report </api/v1/reports/binary/89f39e8f-2305-489a-8d36-be7a7fc375f0>`_
- `Summary </api/v1/reports/binary/89f39e8f-2305-489a-8d36-be7a7fc375f0/summary>`_
- `Installation Footprint </api/v1/reports/binary/89f39e8f-2305-489a-8d36-be7a7fc375f0/footprint>`_
- `Runtime Behavior </api/v1/reports/binary/89f39e8f-2305-489a-8d36-be7a7fc375f0/runtime>`_

**File UUID:** ``89f39e8f-2305-489a-8d36-be7a7fc375f0``

.. note::
   This analysis was generated using the TurdParty malware analysis platform.
   All data is collected in a controlled VM environment with comprehensive monitoring.

