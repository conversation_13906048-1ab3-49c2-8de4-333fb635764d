
Firefox Analysis Report
=======================

Binary Information
------------------


Execution Summary
-----------------


Installation Footprint
-----------------------



Runtime Behavior
-----------------


Security Analysis
-----------------



ECS Data Collection
-------------------



Evidence Box
------------

**Direct Data Access:**

- `Elasticsearch Query <http://localhost:9200/turdparty-rich-cli-ecs-*/_search?q=turdparty.binary_name:firefox>`_
- `Kibana Dashboard <http://localhost:5601/app/discover#/?_g=(filters:!(),time:(from:now-1h,to:now))&_a=(query:(match:(turdparty.binary_name:firefox)))>`_

**API Endpoints:**

- `Full Report </api/v1/reports/binary/c1a4d374-3c67-4cf4-aaf9-2fd29274c60f>`_
- `Summary </api/v1/reports/binary/c1a4d374-3c67-4cf4-aaf9-2fd29274c60f/summary>`_
- `Installation Footprint </api/v1/reports/binary/c1a4d374-3c67-4cf4-aaf9-2fd29274c60f/footprint>`_
- `Runtime Behavior </api/v1/reports/binary/c1a4d374-3c67-4cf4-aaf9-2fd29274c60f/runtime>`_

**File UUID:** ``c1a4d374-3c67-4cf4-aaf9-2fd29274c60f``

.. note::
   This analysis was generated using the TurdParty malware analysis platform.
   All data is collected in a controlled VM environment with comprehensive monitoring.

