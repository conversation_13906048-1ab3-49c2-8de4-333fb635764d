
Nodejs Analysis Report
======================

Binary Information
------------------


Execution Summary
-----------------


Installation Footprint
-----------------------



Runtime Behavior
-----------------


Security Analysis
-----------------



ECS Data Collection
-------------------



Evidence Box
------------

**Direct Data Access:**

- `Elasticsearch Query <http://localhost:9200/turdparty-rich-cli-ecs-*/_search?q=turdparty.binary_name:nodejs>`_
- `Kibana Dashboard <http://localhost:5601/app/discover#/?_g=(filters:!(),time:(from:now-1h,to:now))&_a=(query:(match:(turdparty.binary_name:nodejs)))>`_

**API Endpoints:**

- `Full Report </api/v1/reports/binary/43ebc661-a739-4891-962c-20719e24c2d6>`_
- `Summary </api/v1/reports/binary/43ebc661-a739-4891-962c-20719e24c2d6/summary>`_
- `Installation Footprint </api/v1/reports/binary/43ebc661-a739-4891-962c-20719e24c2d6/footprint>`_
- `Runtime Behavior </api/v1/reports/binary/43ebc661-a739-4891-962c-20719e24c2d6/runtime>`_

**File UUID:** ``43ebc661-a739-4891-962c-20719e24c2d6``

.. note::
   This analysis was generated using the TurdParty malware analysis platform.
   All data is collected in a controlled VM environment with comprehensive monitoring.

