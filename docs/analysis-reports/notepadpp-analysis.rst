
Notepadpp Analysis Report
=========================

Binary Information
------------------


Execution Summary
-----------------


Installation Footprint
-----------------------



Runtime Behavior
-----------------


Security Analysis
-----------------



ECS Data Collection
-------------------



Evidence Box
------------

**Direct Data Access:**

- `Elasticsearch Query <http://localhost:9200/turdparty-rich-cli-ecs-*/_search?q=turdparty.binary_name:notepadpp>`_
- `Kibana Dashboard <http://localhost:5601/app/discover#/?_g=(filters:!(),time:(from:now-1h,to:now))&_a=(query:(match:(turdparty.binary_name:notepadpp)))>`_

**API Endpoints:**

- `Full Report </api/v1/reports/binary/1d86ef59-5996-409a-9b32-402c0087a7e2>`_
- `Summary </api/v1/reports/binary/1d86ef59-5996-409a-9b32-402c0087a7e2/summary>`_
- `Installation Footprint </api/v1/reports/binary/1d86ef59-5996-409a-9b32-402c0087a7e2/footprint>`_
- `Runtime Behavior </api/v1/reports/binary/1d86ef59-5996-409a-9b32-402c0087a7e2/runtime>`_

**File UUID:** ``1d86ef59-5996-409a-9b32-402c0087a7e2``

.. note::
   This analysis was generated using the TurdParty malware analysis platform.
   All data is collected in a controlled VM environment with comprehensive monitoring.

