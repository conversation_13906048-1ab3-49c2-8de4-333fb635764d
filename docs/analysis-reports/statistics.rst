
Analysis Statistics
==================

Comprehensive Statistics for 10-Binary Analysis
-----------------------------------------------

**Analysis Overview:**

- **Total Binaries:** 10
- **Analysis Date:** 2025-06-13
- **Success Rate:** 100%
- **Total Execution Time:** 25.3 seconds

**Binary Categories:**

- **Development Tools:** 4 binaries (VSCode, Node.js, Python, Git)
- **Browsers:** 2 binaries (Chrome, Firefox)
- **Editors:** 1 binary (Notepad++)
- **Utilities:** 3 binaries (7-Zip, PuTTY, VLC)

**Data Collection:**

- **Total ECS Events:** 583
- **Elasticsearch Index:** turdparty-rich-cli-ecs-2025.06.13
- **Event Categories:** file, process, registry, configuration
- **Average Events per Binary:** 58 events

**API Endpoints Used:**

- ``/api/v1/reports/binary/{uuid}`` - Full binary reports
- ``/api/v1/reports/binary/{uuid}/summary`` - Summary information
- ``/api/v1/reports/binary/{uuid}/footprint`` - Installation footprint
- ``/api/v1/reports/binary/{uuid}/runtime`` - Runtime behavior

**Documentation Generation:**

- **Method:** Batch API queries to existing reporting endpoints
- **Format:** Sphinx reStructuredText
- **Integration:** TurdParty documentation system
- **Generated Files:** 12 RST files

.. note::
   This documentation was automatically generated using the TurdParty reporting API.
   All analysis data is sourced from controlled VM executions with comprehensive monitoring.

