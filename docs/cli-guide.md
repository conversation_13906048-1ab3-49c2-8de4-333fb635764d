# 💩🎉TurdParty🎉💩 CLI Guide

A beautiful, modern command-line interface for managing TurdParty malware analysis services, built with Click and Rich libraries for a BTOP-like aesthetic.

## 🚀 Quick Start

### Prerequisites

The CLI uses <PERSON> for dependency management. All required packages are included in the Nix shell:

```bash
# Enter the Nix development environment
nix-shell

# The CLI is now available
./turdparty --help
```

### Dependencies Included in Nix

- `python312Packages.click` - Command-line interface framework
- `python312Packages.rich` - Beautiful terminal output
- `python312Packages.requests` - HTTP client for API calls

## 📋 Available Commands

### 🏠 Main Dashboard

```bash
# Show the main service dashboard (default command)
./turdparty
./turdparty status
```

### 🚀 Service Management

```bash
# Start all services with dependency validation
./turdparty start

# Start services and show logs
./turdparty start --logs

# Stop all services
./turdparty stop

# Restart a specific service
./turdparty restart api
./turdparty restart database
```

### 📊 Monitoring & Status

```bash
# Real-time monitoring dashboard (like BTOP)
./turdparty monitor

# Monitor with custom refresh interval (5 seconds)
./turdparty monitor -i 5

# Show detailed service status
./turdparty status
```

### 🔍 Health Checks

```bash
# Run comprehensive dependency checks
./turdparty check

# This validates:
# - Traefik container status
# - Traefik API accessibility
# - Network configuration
# - Service discovery
```

### 📋 Logs

```bash
# Show logs for a specific service
./turdparty logs api

# Follow logs in real-time
./turdparty logs api -f

# Show last 100 lines
./turdparty logs api -n 100

# Follow logs for multiple services
./turdparty logs api -f
./turdparty logs database -f
```

### 🏗️ Advanced Operations

```bash
# Rebuild all services from scratch
./turdparty rebuild

# Run the parallel test suite
./turdparty test
```

## 🎨 CLI Features

### Beautiful Interface

- **Rich Terminal Output**: Colorful, formatted output using the Rich library
- **Progress Bars**: Visual progress indicators for long-running operations
- **Status Tables**: Beautiful service status tables with emojis and colors
- **Real-time Updates**: Live monitoring dashboard with automatic refresh

### BTOP-like Aesthetics

- **Color-coded Status**: 🟢 Green for healthy, 🟡 Yellow for warnings, 🔴 Red for errors
- **Box Layouts**: Organized panels and sections
- **Live Monitoring**: Real-time service status updates
- **Interactive Elements**: Confirmation prompts and user input

### Smart Dependency Management

- **Nix Integration**: Automatic dependency detection in Nix shell
- **Fallback Support**: Works with system Python if Nix unavailable
- **Clear Error Messages**: Helpful guidance when dependencies missing

## 🔧 Technical Details

### Architecture

```
scripts/turdparty              # Main wrapper script (Bash)
├── Dependency checking        # Nix-aware dependency validation
├── Environment setup          # Project directory validation
└── CLI execution             # Python CLI with Rich interface

scripts/turdparty-cli.py       # Core CLI implementation (Python)
├── Click commands            # Command-line interface framework
├── Rich components           # Beautiful terminal output
├── Service management        # Docker Compose integration
└── Real-time monitoring      # Live dashboard updates
```

### Integration with Existing Scripts

The CLI leverages existing TurdParty scripts:

- `scripts/check-traefik-dependency.sh` - Dependency validation
- `scripts/start-turdparty.sh` - Service startup
- `scripts/rebuild-services.sh` - Service rebuilding
- `scripts/run-parallel-tests.sh` - Test execution

### Error Handling

- **Graceful Failures**: Clear error messages with suggested solutions
- **Dependency Validation**: Immediate feedback on missing requirements
- **Service Status**: Real-time health monitoring with detailed diagnostics

## 🎯 Usage Examples

### Daily Workflow

```bash
# 1. Enter development environment
nix-shell

# 2. Check system health
./turdparty check

# 3. Start services
./turdparty start --logs

# 4. Monitor in real-time
./turdparty monitor

# 5. View specific service logs
./turdparty logs api -f
```

### Troubleshooting

```bash
# Check if Traefik is running
./turdparty check

# Restart problematic service
./turdparty restart api

# Rebuild everything if needed
./turdparty rebuild

# Run tests to verify functionality
./turdparty test
```

### Development Workflow

```bash
# Start services for development
./turdparty start

# Monitor services while developing
./turdparty monitor -i 2

# Restart API after code changes
./turdparty restart api

# Run tests after changes
./turdparty test
```

## 🎉 Benefits

### For Developers

- **Faster Workflow**: Single command for complex operations
- **Visual Feedback**: Beautiful, informative output
- **Error Prevention**: Dependency validation before operations
- **Real-time Monitoring**: Live service status updates

### For Operations

- **Consistent Interface**: Standardized commands across environments
- **Comprehensive Checks**: Built-in health validation
- **Clear Status**: Visual service health indicators
- **Easy Troubleshooting**: Integrated log viewing and service management

### For Users

- **Modern UX**: BTOP-like interface with rich colors and layouts
- **Intuitive Commands**: Self-documenting command structure
- **Helpful Output**: Clear success/error messages with guidance
- **Interactive Elements**: Confirmation prompts for destructive operations

The 💩🎉TurdParty🎉💩 CLI provides a modern, beautiful interface for managing the malware analysis platform, making complex operations simple and visually appealing!
