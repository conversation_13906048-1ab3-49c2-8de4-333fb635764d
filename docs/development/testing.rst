💩🎉TurdParty🎉💩 Testing Framework
===================================

Overview
--------

TurdParty uses a comprehensive testing framework with parallel execution, multiple test types, and extensive coverage reporting. The testing infrastructure is designed for reliability, speed, and comprehensive validation of the malware analysis platform.

Test Architecture
-----------------

Test Categories
~~~~~~~~~~~~~~~

.. list-table::
   :header-rows: 1
   :widths: 25 25 50

   * - Test Type
     - Location
     - Purpose
   * - **Unit Tests**
     - ``tests/unit/``
     - Individual component testing
   * - **Integration Tests**
     - ``tests/integration/``
     - Service interaction testing
   * - **Property Tests**
     - ``tests/property/``
     - Property-based testing with Hypothesis
   * - **Performance Tests**
     - ``tests/performance/``
     - Benchmark and load testing
   * - **WebSocket Tests**
     - ``tests/websocket/``
     - Real-time communication testing
   * - **End-to-End Tests**
     - ``tests/e2e/``
     - Complete workflow testing

Parallel Test Execution
~~~~~~~~~~~~~~~~~~~~~~~~

<PERSON><PERSON><PERSON><PERSON><PERSON> uses a custom parallel test runner for fast execution:

.. code-block:: bash

   # Run full parallel test suite
   tp-test
   
   # Or directly
   ./scripts/run-parallel-tests.sh

**Test Suite Configuration:**

.. list-table::
   :header-rows: 1
   :widths: 30 20 50

   * - Test Suite
     - Execution Time
     - Description
   * - **vm_metrics**
     - ~3s
     - VM monitoring and metrics collection
   * - **websocket_integration**
     - ~39s
     - WebSocket functionality testing
   * - **api_endpoints**
     - ~1s
     - REST API endpoint validation
   * - **grpc_connectivity**
     - ~1s
     - gRPC service communication
   * - **performance_benchmarks**
     - ~1s
     - Performance baseline testing
   * - **ecs_logging**
     - ~4s
     - Elasticsearch logging validation

Quick Start
-----------

Running Tests
~~~~~~~~~~~~~

**Full Test Suite:**

.. code-block:: bash

   # Enter Nix development environment
   nix-shell
   
   # Run all tests in parallel (recommended)
   tp-test
   
   # Expected output:
   # ================================
   # TurdParty VM WebSocket Parallel Test Suite
   # ================================
   # Total Tests: 6
   # Passed: 5
   # Failed: 1
   # Success Rate: 83%

**Individual Test Categories:**

.. code-block:: bash

   # Unit tests
   python -m pytest tests/unit/ -v
   
   # Integration tests
   python -m pytest tests/integration/ -v
   
   # Property-based tests
   python -m pytest tests/property/ -v
   
   # Performance benchmarks
   python -m pytest tests/performance/ --benchmark-only
   
   # WebSocket tests
   python -m pytest tests/websocket/ -v

**Coverage Reporting:**

.. code-block:: bash

   # Generate HTML coverage report
   python -m pytest --cov=. --cov-report=html
   
   # Generate XML coverage report
   python -m pytest --cov=. --cov-report=xml
   
   # View coverage report
   open htmlcov/index.html  # macOS
   xdg-open htmlcov/index.html  # Linux

Test Configuration
------------------

Pytest Configuration
~~~~~~~~~~~~~~~~~~~~

**pytest.ini:**

.. code-block:: ini

   [tool:pytest]
   testpaths = tests
   python_files = test_*.py *_test.py
   python_classes = Test*
   python_functions = test_*
   addopts = 
       --strict-markers
       --strict-config
       --verbose
       --tb=short
       --cov-report=term-missing
       --cov-report=html:htmlcov
       --cov-report=xml:coverage.xml
       --cov-fail-under=80
   markers =
       unit: Unit tests
       integration: Integration tests
       property: Property-based tests
       performance: Performance tests
       websocket: WebSocket tests
       e2e: End-to-end tests
       slow: Slow tests (skip during development)

**Coverage Configuration:**

.. code-block:: ini

   [tool:coverage:run]
   source = .
   omit = 
       */tests/*
       */venv/*
       */.nix-zsh/*
       */htmlcov/*
       setup.py
   
   [tool:coverage:report]
   exclude_lines =
       pragma: no cover
       def __repr__
       raise AssertionError
       raise NotImplementedError

Test Environment
~~~~~~~~~~~~~~~~

**Environment Variables:**

.. code-block:: bash

   # Test Configuration
   TESTING=true
   TEST_DATABASE_URL=sqlite:///test.db
   TEST_REDIS_URL=redis://localhost:6379/1
   TEST_ELASTICSEARCH_URL=http://localhost:9200
   
   # API Testing
   API_BASE_URL=http://localhost:8000
   WEBSOCKET_BASE_URL=ws://localhost:8000
   
   # Performance Testing
   BENCHMARK_ITERATIONS=100
   LOAD_TEST_USERS=10
   LOAD_TEST_DURATION=60

**Test Data Management:**

.. code-block:: bash

   # Test fixtures directory
   tests/fixtures/
   ├── sample_files/          # Sample malware files
   ├── api_responses/         # Mock API responses
   ├── vm_configs/           # VM configuration templates
   └── test_data.json        # Test data sets

Unit Testing
------------

Test Structure
~~~~~~~~~~~~~~

**Example Unit Test:**

.. code-block:: python

   import pytest
   from unittest.mock import Mock, patch
   from api.services.vm_service import VMService
   
   class TestVMService:
       """Unit tests for VM service."""
       
       @pytest.fixture
       def vm_service(self):
           """Create VM service instance for testing."""
           return VMService()
       
       @pytest.fixture
       def mock_docker_client(self):
           """Mock Docker client."""
           with patch('docker.from_env') as mock:
               yield mock.return_value
       
       def test_create_vm_success(self, vm_service, mock_docker_client):
           """Test successful VM creation."""
           # Arrange
           vm_config = {
               "name": "test-vm",
               "template": "ubuntu:20.04",
               "memory_mb": 1024
           }
           mock_container = Mock()
           mock_container.id = "test-container-id"
           mock_docker_client.containers.run.return_value = mock_container
           
           # Act
           result = vm_service.create_vm(vm_config)
           
           # Assert
           assert result["status"] == "created"
           assert result["vm_id"] == "test-container-id"
           mock_docker_client.containers.run.assert_called_once()
       
       def test_create_vm_failure(self, vm_service, mock_docker_client):
           """Test VM creation failure handling."""
           # Arrange
           vm_config = {"name": "test-vm"}
           mock_docker_client.containers.run.side_effect = Exception("Docker error")
           
           # Act & Assert
           with pytest.raises(VMCreationError):
               vm_service.create_vm(vm_config)

**Parametrized Tests:**

.. code-block:: python

   @pytest.mark.parametrize("vm_type,expected_template", [
       ("docker", "ubuntu:20.04"),
       ("vagrant", "ubuntu/focal64"),
       ("custom", "custom-template")
   ])
   def test_vm_template_selection(vm_service, vm_type, expected_template):
       """Test VM template selection for different types."""
       template = vm_service.get_template(vm_type)
       assert template == expected_template

Integration Testing
-------------------

API Integration Tests
~~~~~~~~~~~~~~~~~~~~

**Example API Test:**

.. code-block:: python

   import pytest
   import httpx
   from fastapi.testclient import TestClient
   from api.main import app
   
   class TestVMAPI:
       """Integration tests for VM API endpoints."""
       
       @pytest.fixture
       def client(self):
           """Create test client."""
           return TestClient(app)
       
       @pytest.fixture
       def test_vm_config(self):
           """Test VM configuration."""
           return {
               "name": "test-vm",
               "template": "ubuntu:20.04",
               "vm_type": "docker",
               "memory_mb": 1024,
               "cpus": 2
           }
       
       def test_create_vm_endpoint(self, client, test_vm_config):
           """Test VM creation endpoint."""
           response = client.post("/api/v1/vms/", json=test_vm_config)
           
           assert response.status_code == 201
           data = response.json()
           assert "vm_id" in data
           assert data["status"] == "created"
           assert data["name"] == test_vm_config["name"]
       
       def test_get_vm_status(self, client, test_vm_config):
           """Test VM status retrieval."""
           # Create VM
           create_response = client.post("/api/v1/vms/", json=test_vm_config)
           vm_id = create_response.json()["vm_id"]
           
           # Get status
           status_response = client.get(f"/api/v1/vms/{vm_id}")
           
           assert status_response.status_code == 200
           data = status_response.json()
           assert data["vm_id"] == vm_id
           assert "status" in data

**Database Integration Tests:**

.. code-block:: python

   import pytest
   from sqlalchemy import create_engine
   from sqlalchemy.orm import sessionmaker
   from api.database import Base, get_db
   from api.models import VM
   
   @pytest.fixture
   def test_db():
       """Create test database."""
       engine = create_engine("sqlite:///test.db")
       Base.metadata.create_all(engine)
       SessionLocal = sessionmaker(bind=engine)
       
       yield SessionLocal()
       
       Base.metadata.drop_all(engine)
   
   def test_vm_crud_operations(test_db):
       """Test VM CRUD operations."""
       # Create
       vm = VM(name="test-vm", template="ubuntu:20.04")
       test_db.add(vm)
       test_db.commit()
       
       # Read
       retrieved_vm = test_db.query(VM).filter(VM.name == "test-vm").first()
       assert retrieved_vm is not None
       assert retrieved_vm.template == "ubuntu:20.04"
       
       # Update
       retrieved_vm.status = "running"
       test_db.commit()
       
       # Delete
       test_db.delete(retrieved_vm)
       test_db.commit()
       
       assert test_db.query(VM).filter(VM.name == "test-vm").first() is None

WebSocket Testing
-----------------

WebSocket Integration Tests
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Example WebSocket Test:**

.. code-block:: python

   import pytest
   import asyncio
   import websockets
   import json
   from api.main import app
   
   class TestWebSocketAPI:
       """WebSocket integration tests."""
       
       @pytest.fixture
       def vm_id(self):
           """Create test VM and return ID."""
           # Implementation to create test VM
           return "test-vm-id"
       
       @pytest.mark.asyncio
       async def test_metrics_stream(self, vm_id):
           """Test VM metrics streaming."""
           uri = f"ws://localhost:8000/api/v1/vms/{vm_id}/metrics/stream"
           
           async with websockets.connect(uri) as websocket:
               # Wait for connection message
               message = await websocket.recv()
               data = json.loads(message)
               
               assert data["type"] == "connection_established"
               assert data["vm_id"] == vm_id
               
               # Wait for metrics message
               message = await websocket.recv()
               data = json.loads(message)
               
               assert data["type"] == "metrics"
               assert "cpu_percent" in data
               assert "memory_percent" in data
       
       @pytest.mark.asyncio
       async def test_command_execution(self, vm_id):
           """Test command execution via WebSocket."""
           uri = f"ws://localhost:8000/api/v1/vms/{vm_id}/commands/execute"
           
           async with websockets.connect(uri) as websocket:
               # Send command
               command = {
                   "type": "command",
                   "command": "echo 'Hello World'",
                   "timeout": 30
               }
               await websocket.send(json.dumps(command))
               
               # Receive output
               message = await websocket.recv()
               data = json.loads(message)
               
               assert data["type"] == "stdout"
               assert "Hello World" in data["output"]

Property-Based Testing
----------------------

Hypothesis Integration
~~~~~~~~~~~~~~~~~~~~~

**Example Property Test:**

.. code-block:: python

   import pytest
   from hypothesis import given, strategies as st
   from api.utils.validation import validate_vm_config
   
   class TestVMConfigValidation:
       """Property-based tests for VM configuration validation."""
       
       @given(
           name=st.text(min_size=1, max_size=50, alphabet=st.characters(whitelist_categories=('Lu', 'Ll', 'Nd', 'Pc'))),
           memory_mb=st.integers(min_value=512, max_value=16384),
           cpus=st.integers(min_value=1, max_value=8)
       )
       def test_valid_vm_config_properties(self, name, memory_mb, cpus):
           """Test that valid VM configurations are accepted."""
           config = {
               "name": name,
               "template": "ubuntu:20.04",
               "memory_mb": memory_mb,
               "cpus": cpus
           }
           
           result = validate_vm_config(config)
           assert result.is_valid
           assert result.errors == []
       
       @given(
           memory_mb=st.integers(max_value=511) | st.integers(min_value=32769)
       )
       def test_invalid_memory_rejected(self, memory_mb):
           """Test that invalid memory values are rejected."""
           config = {
               "name": "test-vm",
               "template": "ubuntu:20.04",
               "memory_mb": memory_mb,
               "cpus": 2
           }
           
           result = validate_vm_config(config)
           assert not result.is_valid
           assert any("memory" in error.lower() for error in result.errors)

Performance Testing
-------------------

Benchmark Tests
~~~~~~~~~~~~~~

**Example Benchmark Test:**

.. code-block:: python

   import pytest
   from api.services.vm_service import VMService
   
   class TestVMPerformance:
       """Performance tests for VM operations."""
       
       @pytest.fixture
       def vm_service(self):
           return VMService()
       
       def test_vm_creation_performance(self, benchmark, vm_service):
           """Benchmark VM creation performance."""
           vm_config = {
               "name": "benchmark-vm",
               "template": "ubuntu:20.04",
               "memory_mb": 1024,
               "cpus": 2
           }
           
           result = benchmark(vm_service.create_vm, vm_config)
           
           # Assert performance requirements
           assert benchmark.stats.stats.mean < 5.0  # Less than 5 seconds
           assert result["status"] == "created"
       
       @pytest.mark.parametrize("concurrent_vms", [1, 5, 10])
       def test_concurrent_vm_creation(self, vm_service, concurrent_vms):
           """Test concurrent VM creation performance."""
           import concurrent.futures
           import time
           
           start_time = time.time()
           
           with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_vms) as executor:
               futures = []
               for i in range(concurrent_vms):
                   config = {
                       "name": f"concurrent-vm-{i}",
                       "template": "ubuntu:20.04",
                       "memory_mb": 1024,
                       "cpus": 1
                   }
                   future = executor.submit(vm_service.create_vm, config)
                   futures.append(future)
               
               results = [future.result() for future in futures]
           
           end_time = time.time()
           duration = end_time - start_time
           
           # Assert all VMs created successfully
           assert len(results) == concurrent_vms
           assert all(result["status"] == "created" for result in results)
           
           # Assert reasonable performance
           assert duration < concurrent_vms * 2  # Less than 2 seconds per VM

Load Testing
~~~~~~~~~~~

**Example Load Test:**

.. code-block:: python

   import pytest
   import asyncio
   import aiohttp
   from concurrent.futures import ThreadPoolExecutor
   
   class TestAPILoad:
       """Load tests for API endpoints."""
       
       @pytest.mark.asyncio
       async def test_api_load_handling(self):
           """Test API performance under load."""
           async def make_request(session, url):
               async with session.get(url) as response:
                   return response.status
           
           url = "http://localhost:8000/api/v1/health"
           concurrent_requests = 100
           
           async with aiohttp.ClientSession() as session:
               tasks = [make_request(session, url) for _ in range(concurrent_requests)]
               results = await asyncio.gather(*tasks)
           
           # Assert all requests succeeded
           assert all(status == 200 for status in results)
           assert len(results) == concurrent_requests

Test Data Management
--------------------

Fixtures and Factories
~~~~~~~~~~~~~~~~~~~~~~

**Test Fixtures:**

.. code-block:: python

   import pytest
   from factory import Factory, Faker, SubFactory
   from api.models import VM, User
   
   class UserFactory(Factory):
       """Factory for creating test users."""
       class Meta:
           model = User
       
       username = Faker('user_name')
       email = Faker('email')
       is_active = True
   
   class VMFactory(Factory):
       """Factory for creating test VMs."""
       class Meta:
           model = VM
       
       name = Faker('word')
       template = "ubuntu:20.04"
       memory_mb = 1024
       cpus = 2
       owner = SubFactory(UserFactory)
   
   @pytest.fixture
   def test_user():
       """Create test user."""
       return UserFactory()
   
   @pytest.fixture
   def test_vm(test_user):
       """Create test VM."""
       return VMFactory(owner=test_user)

**Mock Data:**

.. code-block:: python

   import pytest
   from unittest.mock import Mock, patch
   
   @pytest.fixture
   def mock_docker_response():
       """Mock Docker API response."""
       return {
           "Id": "test-container-id",
           "State": {"Status": "running"},
           "Config": {"Image": "ubuntu:20.04"},
           "NetworkSettings": {"IPAddress": "**********"}
       }
   
   @pytest.fixture
   def mock_elasticsearch_response():
       """Mock Elasticsearch response."""
       return {
           "took": 5,
           "hits": {
               "total": {"value": 100},
               "hits": [
                   {
                       "_source": {
                           "timestamp": "2025-06-14T10:00:00Z",
                           "level": "INFO",
                           "message": "Test log message"
                       }
                   }
               ]
           }
       }

Continuous Integration
----------------------

GitHub Actions Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Test Workflow:**

.. code-block:: yaml

   name: Test Suite
   
   on: [push, pull_request]
   
   jobs:
     test:
       runs-on: ubuntu-latest
       
       steps:
       - uses: actions/checkout@v3
       
       - name: Install Nix
         uses: cachix/install-nix-action@v20
       
       - name: Enter Nix Shell and Run Tests
         run: |
           nix-shell --run "tp-test"
       
       - name: Upload Coverage
         uses: codecov/codecov-action@v3
         with:
           file: ./coverage.xml

**Quality Checks:**

.. code-block:: yaml

   name: Code Quality
   
   on: [push, pull_request]
   
   jobs:
     quality:
       runs-on: ubuntu-latest
       
       steps:
       - uses: actions/checkout@v3
       
       - name: Install Nix
         uses: cachix/install-nix-action@v20
       
       - name: Run Quality Checks
         run: |
           nix-shell --run "ruff check ."
           nix-shell --run "ruff format --check ."
           nix-shell --run "mypy ."
           nix-shell --run "bandit -r ."

Best Practices
--------------

Test Writing Guidelines
~~~~~~~~~~~~~~~~~~~~~~

1. **Follow AAA Pattern** - Arrange, Act, Assert
2. **Use descriptive test names** - Explain what is being tested
3. **Test one thing at a time** - Single responsibility per test
4. **Use fixtures for setup** - Avoid code duplication
5. **Mock external dependencies** - Isolate units under test
6. **Test edge cases** - Include boundary conditions
7. **Maintain test independence** - Tests should not depend on each other

Performance Guidelines
~~~~~~~~~~~~~~~~~~~~~

1. **Run tests in parallel** - Use tp-test for full suite
2. **Use appropriate test markers** - Skip slow tests during development
3. **Mock expensive operations** - Database, network, file I/O
4. **Use test databases** - Separate from development data
5. **Clean up resources** - Prevent test pollution

Coverage Guidelines
~~~~~~~~~~~~~~~~~~

1. **Maintain 80%+ coverage** - Enforced by configuration
2. **Focus on critical paths** - Prioritize important functionality
3. **Test error conditions** - Include failure scenarios
4. **Document uncovered code** - Use pragma: no cover sparingly
5. **Review coverage reports** - Identify gaps regularly

The testing framework provides comprehensive validation of TurdParty functionality with fast execution, detailed reporting, and reliable results for confident development and deployment.
