#!/usr/bin/env python3
"""
Fix RST formatting issues in Sphinx reports and regenerate with proper navigation.
"""

import os
import sys
from pathlib import Path

def fix_rst_file(file_path):
    """Fix RST formatting issues in a single file."""
    print(f"🔧 Fixing {file_path.name}...")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Fix the main issues:
    # 1. Add proper spacing after list items
    # 2. Fix indentation issues
    # 3. Ensure proper blank lines
    
    lines = content.split('\n')
    fixed_lines = []
    
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # Fix list items that need proper spacing
        if line.strip().startswith('- **') and line.strip().endswith(':'):
            fixed_lines.append(line)
            # Add blank line after list header
            if i + 1 < len(lines) and not lines[i + 1].strip() == '':
                fixed_lines.append('')
        
        # Fix definition list items
        elif line.strip().startswith(':') and ':' in line[1:]:
            fixed_lines.append(line)
            # Ensure proper spacing after definition lists
            if i + 1 < len(lines) and lines[i + 1].strip() and not lines[i + 1].startswith(':'):
                fixed_lines.append('')
        
        # Fix security features section
        elif '**Security Features:**' in line:
            fixed_lines.append(line)
            fixed_lines.append('')  # Add blank line
        
        else:
            fixed_lines.append(line)
        
        i += 1
    
    # Write fixed content
    with open(file_path, 'w') as f:
        f.write('\n'.join(fixed_lines))
    
    print(f"   ✅ Fixed {file_path.name}")

def regenerate_reports():
    """Regenerate all reports with fixed formatting."""
    sphinx_dir = Path("sphinx-reports")
    
    if not sphinx_dir.exists():
        print("❌ sphinx-reports directory not found!")
        return False
    
    # Fix all RST files
    rst_files = list(sphinx_dir.glob("*.rst"))
    
    for rst_file in rst_files:
        if rst_file.name != "index.rst":  # Skip index for now
            fix_rst_file(rst_file)
    
    print(f"\n🎉 Fixed {len(rst_files)-1} RST files!")
    return True

def main():
    """Main execution."""
    print("🔧 Fixing Sphinx RST formatting issues...")
    
    if regenerate_reports():
        print("\n✅ All RST files have been fixed!")
        print("\n🔧 Now rebuild with:")
        print("   cd sphinx-reports")
        print("   nix-shell -p gnumake -p python311 -p python311Packages.sphinx --run 'make clean && make html'")
        return 0
    else:
        print("❌ Failed to fix RST files")
        return 1

if __name__ == "__main__":
    exit(main())
