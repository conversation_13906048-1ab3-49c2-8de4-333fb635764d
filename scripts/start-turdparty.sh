#!/bin/bash

# 💩🎉TurdParty🎉💩 - Main Startup Script with Dependency Validation
# This script should be used to start TurdParty services with proper dependency checks

set -euo pipefail

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# Function to print colored output
print_colored() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to print banner
print_banner() {
    echo ""
    print_colored $PURPLE "╔══════════════════════════════════════════════════════════════════════════════╗"
    print_colored $PURPLE "║                                                                              ║"
    print_colored $PURPLE "║                        💩🎉 TurdParty Startup 🎉💩                           ║"
    print_colored $PURPLE "║                                                                              ║"
    print_colored $PURPLE "║                    Malware Analysis Platform Startup                        ║"
    print_colored $PURPLE "║                                                                              ║"
    print_colored $PURPLE "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --skip-traefik-check    Skip Traefik dependency check (NOT RECOMMENDED)"
    echo "  --logs                  Show logs after startup"
    echo "  --help, -h              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                      # Start with full dependency checks"
    echo "  $0 --logs               # Start and show logs"
    echo "  $0 --skip-traefik-check # Start without Traefik check (dangerous)"
    echo ""
}

# Function to check if we're in the right directory
check_project_directory() {
    if [ ! -f "docker-compose.yml" ]; then
        print_colored $RED "❌ Please run this script from the TurdParty project root directory"
        print_colored $RED "   Expected to find: docker-compose.yml"
        exit 1
    fi
    
    if [ ! -f "scripts/check-traefik-dependency.sh" ]; then
        print_colored $RED "❌ Traefik dependency checker not found"
        print_colored $RED "   Expected: scripts/check-traefik-dependency.sh"
        exit 1
    fi
    
    print_colored $GREEN "✅ Project directory validated"
}

# Function to run Traefik dependency check
run_traefik_check() {
    local skip_check=$1
    
    if [ "$skip_check" = true ]; then
        print_colored $YELLOW "⚠️  SKIPPING TRAEFIK DEPENDENCY CHECK"
        print_colored $YELLOW "   This is NOT RECOMMENDED and may cause service failures"
        return 0
    fi
    
    print_colored $BLUE "🔍 Running Traefik dependency validation..."
    
    # Source and run the dependency check
    source scripts/check-traefik-dependency.sh
    check_traefik_dependency true true  # Strict mode, exit on failure
}

# Function to start services
start_services() {
    print_colored $BLUE "🚀 Starting TurdParty services..."
    
    # Check if docker-compose is available
    if command -v docker-compose >/dev/null 2>&1; then
        DOCKER_COMPOSE="docker-compose"
    elif docker compose version >/dev/null 2>&1; then
        DOCKER_COMPOSE="docker compose"
    else
        print_colored $RED "❌ Docker Compose not found"
        exit 1
    fi
    
    # Start services in order
    print_colored $BLUE "📦 Starting core infrastructure..."
    $DOCKER_COMPOSE up -d database redis elasticsearch logstash
    
    print_colored $BLUE "⏳ Waiting for infrastructure to be ready..."
    sleep 15
    
    print_colored $BLUE "🚀 Starting application services..."
    $DOCKER_COMPOSE up -d api storage
    
    print_colored $BLUE "⏳ Waiting for application services..."
    sleep 10
    
    print_colored $BLUE "🌐 Starting web services..."
    $DOCKER_COMPOSE up -d frontend kibana
    
    print_colored $BLUE "👷 Starting worker services..."
    $DOCKER_COMPOSE up -d vm-worker file-worker injection-worker workflow-worker
    
    print_colored $GREEN "✅ All services started"
}

# Function to verify services
verify_services() {
    print_colored $BLUE "🔍 Verifying service health..."
    
    local healthy_services=0
    local total_services=0
    
    # Check critical services
    local services=("api" "database" "redis" "elasticsearch" "storage")
    
    for service in "${services[@]}"; do
        total_services=$((total_services + 1))
        if docker ps --filter "name=turdpartycollab_${service}" --format "{{.Names}}" | grep -q "turdpartycollab_${service}"; then
            print_colored $GREEN "✅ $service is running"
            healthy_services=$((healthy_services + 1))
        else
            print_colored $RED "❌ $service is not running"
        fi
    done
    
    print_colored $BLUE "📊 Service Status: $healthy_services/$total_services services healthy"
    
    if [ $healthy_services -eq $total_services ]; then
        print_colored $GREEN "🎉 All critical services are healthy!"
        return 0
    else
        print_colored $YELLOW "⚠️  Some services may not be fully ready yet"
        return 1
    fi
}

# Function to show access information
show_access_info() {
    print_colored $CYAN "🌐 TurdParty Service Access Information:"
    echo ""
    print_colored $WHITE "   📡 API Service:"
    print_colored $CYAN "      • Traefik: https://api.turdparty.localhost"
    print_colored $CYAN "      • Direct:  http://localhost:8000"
    echo ""
    print_colored $WHITE "   🖥️  Frontend:"
    print_colored $CYAN "      • Traefik: https://frontend.turdparty.localhost"
    echo ""
    print_colored $WHITE "   📊 Monitoring:"
    print_colored $CYAN "      • Kibana:  https://kibana.turdparty.localhost"
    print_colored $CYAN "      • Flower:  https://flower.turdparty.localhost"
    echo ""
    print_colored $WHITE "   💾 Storage:"
    print_colored $CYAN "      • MinIO:   https://storage.turdparty.localhost"
    echo ""
    print_colored $YELLOW "💡 Note: Traefik domains require proper /etc/hosts entries or DNS configuration"
}

# Function to show logs
show_logs() {
    print_colored $BLUE "📋 Showing service logs (Ctrl+C to exit)..."
    sleep 2
    
    if command -v docker-compose >/dev/null 2>&1; then
        docker-compose logs -f --tail=50
    else
        docker compose logs -f --tail=50
    fi
}

# Main function
main() {
    local skip_traefik_check=false
    local show_logs_flag=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-traefik-check)
                skip_traefik_check=true
                shift
                ;;
            --logs)
                show_logs_flag=true
                shift
                ;;
            --help|-h)
                show_usage
                exit 0
                ;;
            *)
                print_colored $RED "❌ Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Execute startup sequence
    print_banner
    check_project_directory
    run_traefik_check "$skip_traefik_check"
    start_services
    verify_services
    show_access_info
    
    if [ "$show_logs_flag" = true ]; then
        show_logs
    fi
    
    print_colored $GREEN "🎉 💩🎉TurdParty🎉💩 startup completed successfully!"
    print_colored $GREEN "🚀 System ready for malware analysis operations!"
}

# Run main function with all arguments
main "$@"
