#!/usr/bin/env python3
"""
Test script for VM WebSocket functionality
"""
import asyncio
import websockets
import json
import logging
import sys
import time
import os
from typing import Dict, Any

# Add parent directory to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from utils.service_urls import ServiceURLManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class VMWebSocketTester:
    """Test VM WebSocket endpoints"""

    def __init__(self, base_url: str = None):
        # Use centralized URL manager if no base_url provided
        if base_url is None:
            url_manager = ServiceURLManager('local')
            api_url = url_manager.get_service_url('api')
            self.base_url = api_url.replace('http://', 'ws://')
            self.api_base_url = api_url
        else:
            self.base_url = base_url
            self.api_base_url = base_url.replace('ws://', 'http://')

        self.test_results: Dict[str, Any] = {}
        logger.info(f"Using WebSocket base URL: {self.base_url}")
        logger.info(f"Using API base URL: {self.api_base_url}")
    
    async def test_vm_metrics_stream(self, vm_id: str, vm_type: str = "docker", duration: int = 10):
        """Test VM metrics streaming"""
        logger.info(f"Testing VM metrics stream for {vm_id} (type: {vm_type})")
        
        uri = f"{self.base_url}/api/v1/vms/{vm_id}/metrics/stream?vm_type={vm_type}"
        metrics_received = 0
        start_time = time.time()
        
        try:
            async with websockets.connect(uri) as websocket:
                logger.info(f"Connected to metrics stream: {uri}")
                
                # Collect metrics for specified duration
                while time.time() - start_time < duration:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                        data = json.loads(message)
                        
                        metrics_received += 1
                        
                        # Log first few metrics for verification
                        if metrics_received <= 3:
                            logger.info(f"Received metrics #{metrics_received}: "
                                      f"CPU: {data.get('cpu_percent', 'N/A')}%, "
                                      f"Memory: {data.get('memory_percent', 'N/A')}%, "
                                      f"Status: {data.get('status', 'N/A')}")
                        
                        # Validate metrics structure
                        required_fields = ['vm_id', 'timestamp', 'cpu_percent', 'memory_percent']
                        missing_fields = [field for field in required_fields if field not in data]
                        
                        if missing_fields:
                            logger.warning(f"Missing fields in metrics: {missing_fields}")
                        
                    except asyncio.TimeoutError:
                        logger.warning("Timeout waiting for metrics data")
                        break
                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to parse metrics JSON: {e}")
                        break
                
                self.test_results['metrics_stream'] = {
                    'success': metrics_received > 0,
                    'metrics_received': metrics_received,
                    'duration': time.time() - start_time,
                    'avg_rate': metrics_received / (time.time() - start_time) if time.time() - start_time > 0 else 0
                }
                
                logger.info(f"Metrics test completed: {metrics_received} metrics in {time.time() - start_time:.1f}s")
                
        except Exception as e:
            logger.error(f"Error testing metrics stream: {e}")
            self.test_results['metrics_stream'] = {
                'success': False,
                'error': str(e)
            }
    
    async def test_command_execution(self, vm_id: str, commands: list = None):
        """Test command execution via WebSocket"""
        if commands is None:
            commands = ["echo 'Hello World'", "pwd", "ls -la /tmp"]
        
        logger.info(f"Testing command execution for VM {vm_id}")
        
        uri = f"{self.base_url}/api/v1/vms/{vm_id}/commands/execute"
        commands_executed = 0
        
        try:
            async with websockets.connect(uri) as websocket:
                logger.info(f"Connected to command execution: {uri}")
                
                for command in commands:
                    logger.info(f"Executing command: {command}")
                    
                    # Send command
                    command_data = {
                        "command": command,
                        "working_directory": "/tmp"
                    }
                    await websocket.send(json.dumps(command_data))
                    
                    # Collect output
                    output_parts = []
                    command_complete = False
                    
                    while not command_complete:
                        try:
                            message = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                            data = json.loads(message)
                            
                            output_parts.append(data)
                            
                            if data.get('stdout'):
                                logger.info(f"STDOUT: {data['stdout'].strip()}")
                            if data.get('stderr'):
                                logger.warning(f"STDERR: {data['stderr'].strip()}")
                            
                            if data.get('is_complete'):
                                exit_code = data.get('exit_code', -1)
                                logger.info(f"Command completed with exit code: {exit_code}")
                                command_complete = True
                                commands_executed += 1
                                
                        except asyncio.TimeoutError:
                            logger.error(f"Timeout waiting for command completion: {command}")
                            break
                        except json.JSONDecodeError as e:
                            logger.error(f"Failed to parse command output JSON: {e}")
                            break
                
                self.test_results['command_execution'] = {
                    'success': commands_executed == len(commands),
                    'commands_executed': commands_executed,
                    'total_commands': len(commands)
                }
                
                logger.info(f"Command execution test completed: {commands_executed}/{len(commands)} commands")
                
        except Exception as e:
            logger.error(f"Error testing command execution: {e}")
            self.test_results['command_execution'] = {
                'success': False,
                'error': str(e)
            }
    
    async def test_file_upload_progress(self, vm_id: str):
        """Test file upload progress tracking"""
        logger.info(f"Testing file upload progress for VM {vm_id}")
        
        uri = f"{self.base_url}/api/v1/vms/{vm_id}/files/upload"
        
        try:
            async with websockets.connect(uri) as websocket:
                logger.info(f"Connected to file upload: {uri}")
                
                # Send upload initiation
                upload_info = {
                    "target_path": "/tmp/test_upload.txt"
                }
                await websocket.send(json.dumps(upload_info))
                
                # Wait for ready response
                message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(message)
                
                if data.get('type') == 'upload_ready':
                    logger.info("Upload ready response received")
                    self.test_results['file_upload'] = {
                        'success': True,
                        'ready_received': True
                    }
                else:
                    logger.warning(f"Unexpected response: {data}")
                    self.test_results['file_upload'] = {
                        'success': False,
                        'error': 'Unexpected response'
                    }
                
        except Exception as e:
            logger.error(f"Error testing file upload: {e}")
            self.test_results['file_upload'] = {
                'success': False,
                'error': str(e)
            }
    
    async def create_test_vm(self) -> str:
        """Create a test VM for testing"""
        import httpx
        
        logger.info("Creating test VM...")
        
        vm_data = {
            "name": f"test-vm-{int(time.time())}",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 512,
            "cpus": 1,
            "domain": "TurdParty",
            "description": "Test VM for WebSocket testing"
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.api_base_url}/api/v1/vms/",
                    json=vm_data,
                    timeout=30.0
                )
                
                if response.status_code == 201:
                    vm_info = response.json()
                    vm_id = vm_info['vm_id']
                    logger.info(f"Test VM created successfully: {vm_id}")
                    return vm_id
                else:
                    logger.error(f"Failed to create test VM: {response.status_code} {response.text}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error creating test VM: {e}")
            return None
    
    async def cleanup_test_vm(self, vm_id: str):
        """Clean up test VM"""
        import httpx
        
        logger.info(f"Cleaning up test VM: {vm_id}")
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.delete(
                    f"{self.api_base_url}/api/v1/vms/{vm_id}?force=true",
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    logger.info(f"Test VM cleaned up successfully: {vm_id}")
                else:
                    logger.warning(f"Failed to cleanup test VM: {response.status_code}")
                    
        except Exception as e:
            logger.error(f"Error cleaning up test VM: {e}")
    
    async def run_all_tests(self, vm_id: str = None, cleanup: bool = True):
        """Run all WebSocket tests"""
        logger.info("Starting VM WebSocket tests...")
        
        created_vm = False
        if vm_id is None:
            vm_id = await self.create_test_vm()
            if vm_id is None:
                logger.error("Failed to create test VM, aborting tests")
                return False
            created_vm = True
            
            # Wait a moment for VM to be ready
            await asyncio.sleep(2)
        
        try:
            # Test VM metrics streaming
            await self.test_vm_metrics_stream(vm_id, duration=5)
            
            # Test command execution
            await self.test_command_execution(vm_id)
            
            # Test file upload progress
            await self.test_file_upload_progress(vm_id)
            
            # Print results
            self.print_test_results()
            
            return all(result.get('success', False) for result in self.test_results.values())
            
        finally:
            if created_vm and cleanup:
                await self.cleanup_test_vm(vm_id)
    
    def print_test_results(self):
        """Print test results summary"""
        logger.info("\n" + "="*50)
        logger.info("VM WEBSOCKET TEST RESULTS")
        logger.info("="*50)
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
            logger.info(f"{test_name}: {status}")
            
            if not result.get('success', False) and 'error' in result:
                logger.info(f"  Error: {result['error']}")
            
            # Print additional details
            for key, value in result.items():
                if key not in ['success', 'error']:
                    logger.info(f"  {key}: {value}")
        
        logger.info("="*50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get('success', False))
        logger.info(f"Overall: {passed_tests}/{total_tests} tests passed")


async def main():
    """Main test function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test VM WebSocket functionality')
    parser.add_argument('--vm-id', help='Existing VM ID to test (will create new VM if not provided)')
    parser.add_argument('--base-url', default='ws://localhost:8000', help='Base WebSocket URL')
    parser.add_argument('--no-cleanup', action='store_true', help='Do not cleanup test VM')
    
    args = parser.parse_args()
    
    tester = VMWebSocketTester(args.base_url)
    
    try:
        success = await tester.run_all_tests(
            vm_id=args.vm_id,
            cleanup=not args.no_cleanup
        )
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
