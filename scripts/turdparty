#!/bin/bash

# 💩🎉TurdParty🎉💩 - Sexy CLI Wrapper Script
# This script sets up the environment and runs the TurdParty CLI

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CLI_SCRIPT="$SCRIPT_DIR/turdparty-cli.py"
REQUIREMENTS_FILE="$SCRIPT_DIR/requirements-cli.txt"

# Function to print colored output
print_colored() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check and install Python dependencies
setup_dependencies() {
    print_colored $BLUE "🔍 Checking Python dependencies..."
    
    # Check if pip is available
    if ! command_exists pip3 && ! command_exists pip; then
        print_colored $RED "❌ pip not found. Please install Python pip."
        exit 1
    fi
    
    # Use pip3 if available, otherwise pip
    local pip_cmd="pip3"
    if ! command_exists pip3; then
        pip_cmd="pip"
    fi
    
    # Check if requirements are already installed
    local missing_deps=false
    
    # Check for click
    if ! python3 -c "import click" 2>/dev/null; then
        missing_deps=true
    fi
    
    # Check for rich
    if ! python3 -c "import rich" 2>/dev/null; then
        missing_deps=true
    fi
    
    # Check for requests
    if ! python3 -c "import requests" 2>/dev/null; then
        missing_deps=true
    fi
    
    if [ "$missing_deps" = true ]; then
        print_colored $YELLOW "📦 Installing CLI dependencies..."
        
        # Try to install in user space first
        if $pip_cmd install --user -r "$REQUIREMENTS_FILE" >/dev/null 2>&1; then
            print_colored $GREEN "✅ Dependencies installed successfully"
        else
            print_colored $YELLOW "⚠️  User install failed, trying system install..."
            if sudo $pip_cmd install -r "$REQUIREMENTS_FILE" >/dev/null 2>&1; then
                print_colored $GREEN "✅ Dependencies installed successfully"
            else
                print_colored $RED "❌ Failed to install dependencies. Please install manually:"
                print_colored $CYAN "   $pip_cmd install click rich requests"
                exit 1
            fi
        fi
    else
        print_colored $GREEN "✅ All dependencies are already installed"
    fi
}

# Function to show banner
show_banner() {
    print_colored $PURPLE "╔══════════════════════════════════════════════════════════════════════════════╗"
    print_colored $PURPLE "║                                                                              ║"
    print_colored $PURPLE "║                        💩🎉 TurdParty CLI 🎉💩                              ║"
    print_colored $PURPLE "║                                                                              ║"
    print_colored $PURPLE "║                    Malware Analysis Platform Manager                        ║"
    print_colored $PURPLE "║                                                                              ║"
    print_colored $PURPLE "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to check if we're in the right directory
check_project_directory() {
    if [ ! -f "$PROJECT_ROOT/docker-compose.yml" ]; then
        print_colored $RED "❌ Not in TurdParty project directory!"
        print_colored $RED "   Please run this script from the TurdParty project root."
        exit 1
    fi
}

# Function to check Python version
check_python() {
    if ! command_exists python3; then
        print_colored $RED "❌ Python 3 not found. Please install Python 3.8 or later."
        exit 1
    fi
    
    # Check Python version
    local python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    local major=$(echo $python_version | cut -d. -f1)
    local minor=$(echo $python_version | cut -d. -f2)
    
    if [ "$major" -lt 3 ] || ([ "$major" -eq 3 ] && [ "$minor" -lt 8 ]); then
        print_colored $RED "❌ Python 3.8 or later required. Found: $python_version"
        exit 1
    fi
    
    print_colored $GREEN "✅ Python $python_version detected"
}

# Main function
main() {
    # Check if --help or -h is passed
    if [[ "${1:-}" == "--help" ]] || [[ "${1:-}" == "-h" ]]; then
        show_banner
        print_colored $CYAN "TurdParty CLI - Beautiful command-line interface for managing TurdParty services"
        echo ""
        print_colored $YELLOW "Usage:"
        print_colored $WHITE "  turdparty [COMMAND] [OPTIONS]"
        echo ""
        print_colored $YELLOW "Available Commands:"
        print_colored $WHITE "  start      🚀 Start TurdParty services with dependency validation"
        print_colored $WHITE "  stop       🛑 Stop all TurdParty services"
        print_colored $WHITE "  status     📊 Show detailed service status"
        print_colored $WHITE "  monitor    📊 Real-time service monitoring dashboard"
        print_colored $WHITE "  check      🔍 Run comprehensive dependency and health checks"
        print_colored $WHITE "  logs       📋 Show service logs"
        print_colored $WHITE "  restart    🔄 Restart a specific service"
        print_colored $WHITE "  rebuild    🏗️ Rebuild and restart all services"
        print_colored $WHITE "  test       🧪 Run the parallel test suite"
        echo ""
        print_colored $YELLOW "Examples:"
        print_colored $CYAN "  turdparty start --logs     # Start services and show logs"
        print_colored $CYAN "  turdparty monitor -i 5     # Monitor with 5-second refresh"
        print_colored $CYAN "  turdparty logs api -f      # Follow API logs"
        print_colored $CYAN "  turdparty restart api      # Restart API service"
        echo ""
        print_colored $YELLOW "For detailed help on any command:"
        print_colored $CYAN "  turdparty [COMMAND] --help"
        echo ""
        return 0
    fi
    
    # Show banner for interactive use
    if [ $# -eq 0 ]; then
        show_banner
    fi
    
    # Run checks
    check_project_directory
    check_python
    setup_dependencies
    
    # Change to project root
    cd "$PROJECT_ROOT"
    
    # Run the CLI
    python3 "$CLI_SCRIPT" "$@"
}

# Run main function with all arguments
main "$@"
