#!/bin/bash

# 💩🎉TurdParty🎉💩 API Service Entrypoint with Dependency Checks
# This script replaces the default entrypoint to include critical dependency validation

set -euo pipefail

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_colored() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_colored $PURPLE "🚀 💩🎉TurdParty🎉💩 API Service Starting..."
print_colored $PURPLE "═══════════════════════════════════════════════"

# Run startup dependency checks
if [ -f "/app/scripts/startup-check.sh" ]; then
    print_colored $BLUE "🔍 Running dependency checks..."
    /app/scripts/startup-check.sh
else
    print_colored $YELLOW "⚠️  Startup check script not found - proceeding without validation"
fi

# Start the API service
print_colored $GREEN "🚀 Starting API service with Uvicorn..."
exec uvicorn src.main:app --host 0.0.0.0 --port 8000
