"""
TurdParty API Service - Main Application

Core API service for file upload, MinIO storage, and workflow orchestration.
Focused on the essential workflow: File Upload → MinIO → VM Injection → ELK Data Exfiltration
"""

import logging
import os
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import <PERSON><PERSON><PERSON>, Request, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles

from .utils.logging import (
    setup_logging,
    CorrelationIdMiddleware,
    performance_logger,
    get_correlation_id
)

from .routes import health
from .routes.v1 import v1_router
from .services.database import init_database
from .services.minio_client import init_minio
from .services.celery_app import init_celery

# Configure enhanced logging
setup_logging(
    service_name="turdparty-api",
    log_level=os.getenv("LOG_LEVEL", "INFO"),
    enable_json=os.getenv("LOG_FORMAT", "json").lower() == "json"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(_app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan manager for TurdParty API.

    Handles startup and shutdown events for the FastAPI application,
    including service initialization and cleanup.

    Args:
        _app: The FastAPI application instance

    Yields:
        None: Control to the application during its lifetime

    Note:
        This function initializes database connections, MinIO client,
        and Celery workers during startup, with graceful error handling
        to allow degraded functionality if services are unavailable.
    """
    logger.info("Starting TurdParty API service...")

    # Initialize services with error handling
    try:
        try:
            await init_database()
            logger.info("Database initialized")
        except Exception as e:
            logger.warning(f"Database initialization failed: {e}")

        try:
            await init_minio()
            logger.info("MinIO client initialized")
        except Exception as e:
            logger.warning(f"MinIO initialization failed: {e}")

        try:
            init_celery()
            logger.info("Celery app initialized")
        except Exception as e:
            logger.warning(f"Celery initialization failed: {e}")

        logger.info("TurdParty API service started successfully")

    except Exception as e:
        logger.error(f"Critical failure during startup: {e}")
        # Don't raise - allow service to start with degraded functionality

    yield

    logger.info("Shutting down TurdParty API service...")


def create_application() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Sets up the complete TurdParty API application with all middleware,
    exception handlers, route includes, and static file mounts.

    Returns:
        FastAPI: Fully configured FastAPI application instance

    Note:
        The application includes:
        - Correlation ID middleware for request tracing
        - CORS middleware for cross-origin requests
        - Global exception handler with structured logging
        - Health and v1 API route includes
        - Static documentation mounting (if available)
    """
    
    app = FastAPI(
        title="TurdParty API",
        description="File analysis workflow API - Upload → MinIO → VM Injection → ELK",
        version="1.0.0",
        lifespan=lifespan,
        docs_url="/docs",
        redoc_url="/redoc"
    )

    # CORS middleware (first, before other middleware)
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
        # Explicitly allow WebSocket headers
        expose_headers=["*"],
    )

    # Add correlation ID middleware (after CORS) - with WebSocket support
    app.add_middleware(CorrelationIdMiddleware)
    
    # Exception handler with correlation ID
    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
        correlation_id = get_correlation_id()
        logger.error(
            f"Global exception: {exc}",
            exc_info=True,
            extra={
                "correlation_id": correlation_id,
                "method": request.method,
                "url": str(request.url),
                "exception_type": type(exc).__name__
            }
        )
        return JSONResponse(
            status_code=500,
            content={
                "detail": "Internal server error",
                "correlation_id": correlation_id
            }
        )
    
    # Add WebSocket endpoints at app level (not router level)
    @app.websocket("/test-ws")
    async def test_websocket(websocket: WebSocket):
        await websocket.accept()
        await websocket.send_json({"message": "WebSocket test successful"})
        await websocket.close()

    @app.websocket("/api/v1/vms/{vm_id}/metrics/stream")
    async def stream_vm_metrics_app(websocket: WebSocket, vm_id: str, vm_type: str = "docker"):
        """Stream real-time VM metrics via WebSocket"""
        import uuid
        import asyncio
        from datetime import datetime, timezone

        await websocket.accept()

        try:
            # Send initial connection confirmation
            await websocket.send_json({
                "type": "connection_established",
                "vm_id": vm_id,
                "connection_id": str(uuid.uuid4()),
                "timestamp": datetime.now(timezone.utc).isoformat()
            })

            # Mock metrics streaming for testing
            counter = 0
            while True:
                await asyncio.sleep(1)
                counter += 1

                # Generate mock metrics
                metrics = {
                    "type": "metrics_data",
                    "vm_id": vm_id,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "data": {
                        "cpu_percent": min(100, 10 + (counter % 50)),
                        "memory_percent": min(100, 20 + (counter % 30)),
                        "disk_usage": min(100, 30 + (counter % 20)),
                        "network_io": {
                            "bytes_sent": counter * 1024,
                            "bytes_recv": counter * 512
                        }
                    }
                }

                await websocket.send_json(metrics)

        except Exception as e:
            logger.error(f"Error in metrics stream for VM {vm_id}: {e}")
            try:
                await websocket.send_json({
                    "type": "error",
                    "error": str(e),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                })
            except:
                pass

    @app.websocket("/api/v1/vms/{vm_id}/commands/execute")
    async def execute_command_stream_app(websocket: WebSocket, vm_id: str):
        """Execute commands with real-time output streaming"""
        import json
        import asyncio
        from datetime import datetime, timezone

        await websocket.accept()

        try:
            # Send ready message
            await websocket.send_json({
                "type": "ready",
                "message": "Command execution ready",
                "vm_id": vm_id
            })

            while True:
                # Wait for command from client
                data = await websocket.receive_text()
                command_data = json.loads(data)

                command = command_data.get("command", "")
                working_dir = command_data.get("working_directory", "/tmp")

                if not command:
                    await websocket.send_json({
                        "type": "error",
                        "message": "No command provided"
                    })
                    continue

                # Mock command execution for testing
                await websocket.send_json({
                    "type": "command_start",
                    "command": command,
                    "working_directory": working_dir,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                })

                # Simulate command output
                await asyncio.sleep(0.5)
                await websocket.send_json({
                    "type": "command_output",
                    "stdout": f"Executing: {command}\\nCommand completed successfully\\n",
                    "stderr": "",
                    "exit_code": 0,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                })

        except Exception as e:
            logger.error(f"Error in command execution for VM {vm_id}: {e}")
            try:
                await websocket.send_json({
                    "type": "error",
                    "error": str(e),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                })
            except:
                pass

    @app.websocket("/api/v1/vms/{vm_id}/files/upload")
    async def upload_file_stream_app(websocket: WebSocket, vm_id: str):
        """Upload file to VM with real-time progress via WebSocket"""
        import json
        import asyncio
        from datetime import datetime, timezone

        await websocket.accept()

        try:
            # Send ready message
            await websocket.send_json({
                "type": "upload_ready",
                "message": "Ready to receive file upload",
                "vm_id": vm_id
            })

            # Wait for file upload initiation
            data = await websocket.receive_text()
            upload_info = json.loads(data)

            target_path = upload_info.get("target_path", "/tmp/uploaded_file")

            # Mock file upload progress
            for progress in [10, 25, 50, 75, 90, 100]:
                await asyncio.sleep(0.2)
                await websocket.send_json({
                    "type": "upload_progress",
                    "progress": progress,
                    "target_path": target_path,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                })

            await websocket.send_json({
                "type": "upload_complete",
                "target_path": target_path,
                "message": "File upload completed successfully",
                "timestamp": datetime.now(timezone.utc).isoformat()
            })

        except Exception as e:
            logger.error(f"Error in file upload for VM {vm_id}: {e}")
            try:
                await websocket.send_json({
                    "type": "error",
                    "error": str(e),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                })
            except:
                pass

    # Include routers
    from .routes import celery_health
    app.include_router(health.router, prefix="/health", tags=["health"])
    app.include_router(celery_health.router, prefix="/api/v1")
    app.include_router(v1_router)

    # Mount static documentation files
    docs_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "docs", "_build", "html")
    if os.path.exists(docs_path):
        app.mount("/docs", StaticFiles(directory=docs_path, html=True), name="docs")
        logger.info(f"Documentation mounted at /docs from {docs_path}")
    else:
        logger.warning(f"Documentation directory not found: {docs_path}")

    return app


# Create the application instance
app = create_application()


if __name__ == "__main__":
    import uvicorn
    
    port = int(os.getenv("PORT", "8000"))
    host = os.getenv("HOST", "0.0.0.0")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=True,
        log_level="info"
    )
