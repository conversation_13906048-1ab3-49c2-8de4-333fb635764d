"""
TurdParty API Service - Main Application

Core API service for file upload, MinIO storage, and workflow orchestration.
Focused on the essential workflow: File Upload → MinIO → VM Injection → ELK Data Exfiltration
"""

import logging
import os
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import <PERSON><PERSON><PERSON>, Request, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles

from .utils.logging import (
    setup_logging,
    CorrelationIdMiddleware,
    performance_logger,
    get_correlation_id
)

from .routes import health
from .routes.v1 import v1_router
from .services.database import init_database
from .services.minio_client import init_minio
from .services.celery_app import init_celery

# Configure enhanced logging
setup_logging(
    service_name="turdparty-api",
    log_level=os.getenv("LOG_LEVEL", "INFO"),
    enable_json=os.getenv("LOG_FORMAT", "json").lower() == "json"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(_app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan manager for TurdParty API.

    Handles startup and shutdown events for the FastAPI application,
    including service initialization and cleanup.

    Args:
        _app: The FastAPI application instance

    Yields:
        None: Control to the application during its lifetime

    Note:
        This function initializes database connections, MinIO client,
        and Celery workers during startup, with graceful error handling
        to allow degraded functionality if services are unavailable.
    """
    logger.info("Starting TurdParty API service...")

    # Initialize services with error handling
    try:
        try:
            await init_database()
            logger.info("Database initialized")
        except Exception as e:
            logger.warning(f"Database initialization failed: {e}")

        try:
            await init_minio()
            logger.info("MinIO client initialized")
        except Exception as e:
            logger.warning(f"MinIO initialization failed: {e}")

        try:
            init_celery()
            logger.info("Celery app initialized")
        except Exception as e:
            logger.warning(f"Celery initialization failed: {e}")

        logger.info("TurdParty API service started successfully")

    except Exception as e:
        logger.error(f"Critical failure during startup: {e}")
        # Don't raise - allow service to start with degraded functionality

    yield

    logger.info("Shutting down TurdParty API service...")


def create_application() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Sets up the complete TurdParty API application with all middleware,
    exception handlers, route includes, and static file mounts.

    Returns:
        FastAPI: Fully configured FastAPI application instance

    Note:
        The application includes:
        - Correlation ID middleware for request tracing
        - CORS middleware for cross-origin requests
        - Global exception handler with structured logging
        - Health and v1 API route includes
        - Static documentation mounting (if available)
    """

    # Check environment configuration
    development_mode = os.getenv("DEVELOPMENT_MODE", "true").lower() == "true"
    traefik_enabled = os.getenv("TRAEFIK_ENABLED", "false").lower() == "true"

    logger.info(f"Creating TurdParty API - Development mode: {development_mode}, Traefik: {traefik_enabled}")

    app = FastAPI(
        title="TurdParty API",
        description="File analysis workflow API - Upload → MinIO → VM Injection → ELK",
        version="1.0.0",
        lifespan=lifespan,
        docs_url="/docs",
        redoc_url="/redoc"
    )

    # CORS middleware with WebSocket support
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
        # Explicitly allow WebSocket headers
        expose_headers=["*"],
    )

    # Add correlation ID middleware with WebSocket support
    app.add_middleware(CorrelationIdMiddleware)
    
    # Exception handler with correlation ID
    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
        correlation_id = get_correlation_id()
        logger.error(
            f"Global exception: {exc}",
            exc_info=True,
            extra={
                "correlation_id": correlation_id,
                "method": request.method,
                "url": str(request.url),
                "exception_type": type(exc).__name__
            }
        )
        return JSONResponse(
            status_code=500,
            content={
                "detail": "Internal server error",
                "correlation_id": correlation_id
            }
        )
    
    # Add a simple test WebSocket endpoint at app level
    @app.websocket("/test-ws")
    async def test_websocket(websocket: WebSocket):
        await websocket.accept()
        await websocket.send_json({"message": "WebSocket test successful"})
        await websocket.close()

    # Add development mode info endpoint
    @app.get("/dev/info")
    async def dev_info():
        """Development mode information"""
        dev_mode = os.getenv("DEVELOPMENT_MODE", "true").lower() == "true"
        traefik_on = os.getenv("TRAEFIK_ENABLED", "false").lower() == "true"
        return {
            "mode": "development" if dev_mode else "production",
            "traefik_enabled": traefik_on,
            "development_mode": dev_mode,
            "websocket_enabled": True,
            "environment": os.getenv("ENVIRONMENT", "local"),
            "message": f"API running in {'development' if dev_mode else 'production'} mode"
        }

    # Include routers
    from .routes import celery_health
    app.include_router(health.router, prefix="/health", tags=["health"])
    app.include_router(celery_health.router, prefix="/api/v1")
    app.include_router(v1_router)

    # Mount static documentation files
    docs_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "docs", "_build", "html")
    if os.path.exists(docs_path):
        app.mount("/docs", StaticFiles(directory=docs_path, html=True), name="docs")
        logger.info(f"Documentation mounted at /docs from {docs_path}")
    else:
        logger.warning(f"Documentation directory not found: {docs_path}")

    return app


# Create the application instance
app = create_application()


if __name__ == "__main__":
    import uvicorn
    
    port = int(os.getenv("PORT", "8000"))
    host = os.getenv("HOST", "0.0.0.0")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=True,
        log_level="info"
    )
