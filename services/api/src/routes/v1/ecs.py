"""ECS (Elastic Common Schema) data endpoints for TurdParty API."""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from uuid import UUID

import requests
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/ecs", tags=["ecs"])


class ECSEventResponse(BaseModel):
    """Response model for ECS events."""
    total_events: int
    events: List[Dict[str, Any]]
    index_name: str
    query_time_ms: int


class ECSIndexInfo(BaseModel):
    """Response model for ECS index information."""
    index_name: str
    document_count: int
    size_bytes: int
    health: str


@router.get("/events/{vm_id}", response_model=ECSEventResponse)
async def get_vm_ecs_events(
    vm_id: UUID,
    size: int = Query(default=50, le=1000, description="Number of events to return"),
    from_time: Optional[str] = Query(default=None, description="Start time (ISO format)"),
    to_time: Optional[str] = Query(default=None, description="End time (ISO format)")
) -> ECSEventResponse:
    """Get ECS events for a specific VM."""
    try:
        # Default time range: last 24 hours
        if not from_time:
            from_time = (datetime.utcnow() - timedelta(hours=24)).isoformat() + "Z"
        if not to_time:
            to_time = datetime.utcnow().isoformat() + "Z"

        # Construct Elasticsearch query
        es_query = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"host.name": str(vm_id)}},
                        {
                            "range": {
                                "@timestamp": {
                                    "gte": from_time,
                                    "lte": to_time
                                }
                            }
                        }
                    ]
                }
            },
            "sort": [{"@timestamp": {"order": "desc"}}],
            "size": size
        }

        # Get today's index
        today = datetime.utcnow().strftime('%Y.%m.%d')
        index_name = f"turdparty-vm-ecs-{today}"

        # Query Elasticsearch
        es_url = "http://elasticsearch:9200"
        response = requests.post(
            f"{es_url}/{index_name}/_search",
            json=es_query,
            headers={"Content-Type": "application/json"},
            timeout=30
        )

        if response.status_code == 404:
            # Try wildcard search across all ECS indices
            response = requests.post(
                f"{es_url}/turdparty-vm-ecs-*/_search",
                json=es_query,
                headers={"Content-Type": "application/json"},
                timeout=30
            )

        response.raise_for_status()
        es_data = response.json()

        # Extract events
        events = []
        for hit in es_data.get("hits", {}).get("hits", []):
            events.append(hit["_source"])

        return ECSEventResponse(
            total_events=es_data.get("hits", {}).get("total", {}).get("value", 0),
            events=events,
            index_name=index_name,
            query_time_ms=es_data.get("took", 0)
        )

    except requests.RequestException as e:
        logger.error(f"Elasticsearch request failed: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Failed to query Elasticsearch: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Failed to get ECS events for VM {vm_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve ECS events: {str(e)}"
        )


@router.get("/indices", response_model=List[ECSIndexInfo])
async def get_ecs_indices() -> List[ECSIndexInfo]:
    """Get information about available ECS indices."""
    try:
        es_url = "http://elasticsearch:9200"
        
        # Get index stats
        response = requests.get(
            f"{es_url}/turdparty-vm-ecs-*/_stats",
            timeout=30
        )
        response.raise_for_status()
        stats_data = response.json()

        # Get index health
        health_response = requests.get(
            f"{es_url}/_cat/indices/turdparty-vm-ecs-*?format=json",
            timeout=30
        )
        health_response.raise_for_status()
        health_data = health_response.json()

        # Combine data
        indices = []
        for index_name, index_stats in stats_data.get("indices", {}).items():
            # Find corresponding health data
            health_info = next(
                (h for h in health_data if h["index"] == index_name),
                {"health": "unknown"}
            )
            
            indices.append(ECSIndexInfo(
                index_name=index_name,
                document_count=index_stats.get("total", {}).get("docs", {}).get("count", 0),
                size_bytes=index_stats.get("total", {}).get("store", {}).get("size_in_bytes", 0),
                health=health_info.get("health", "unknown")
            ))

        return sorted(indices, key=lambda x: x.index_name, reverse=True)

    except requests.RequestException as e:
        logger.error(f"Elasticsearch request failed: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Failed to query Elasticsearch: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Failed to get ECS indices: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve ECS indices: {str(e)}"
        )


@router.get("/events/{vm_id}/summary")
async def get_vm_events_summary(vm_id: UUID) -> Dict[str, Any]:
    """Get a summary of events for a specific VM."""
    try:
        es_url = "http://elasticsearch:9200"
        
        # Aggregation query for event summary
        agg_query = {
            "query": {
                "term": {"host.name": str(vm_id)}
            },
            "aggs": {
                "event_types": {
                    "terms": {"field": "event.action.keyword", "size": 20}
                },
                "event_categories": {
                    "terms": {"field": "event.category.keyword", "size": 10}
                },
                "timeline": {
                    "date_histogram": {
                        "field": "@timestamp",
                        "calendar_interval": "1m"
                    }
                }
            },
            "size": 0
        }

        response = requests.post(
            f"{es_url}/turdparty-vm-ecs-*/_search",
            json=agg_query,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        response.raise_for_status()
        data = response.json()

        return {
            "vm_id": str(vm_id),
            "total_events": data.get("hits", {}).get("total", {}).get("value", 0),
            "event_types": data.get("aggregations", {}).get("event_types", {}).get("buckets", []),
            "event_categories": data.get("aggregations", {}).get("event_categories", {}).get("buckets", []),
            "timeline": data.get("aggregations", {}).get("timeline", {}).get("buckets", []),
            "query_time_ms": data.get("took", 0)
        }

    except requests.RequestException as e:
        logger.error(f"Elasticsearch request failed: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Failed to query Elasticsearch: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Failed to get events summary for VM {vm_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve events summary: {str(e)}"
        )


@router.get("/health")
async def check_ecs_health() -> Dict[str, Any]:
    """Check Elasticsearch health and connectivity."""
    try:
        es_url = "http://elasticsearch:9200"
        
        # Check cluster health
        response = requests.get(f"{es_url}/_cluster/health", timeout=10)
        response.raise_for_status()
        health_data = response.json()

        # Check if ECS indices exist
        indices_response = requests.get(
            f"{es_url}/_cat/indices/turdparty-vm-ecs-*?format=json",
            timeout=10
        )
        indices_count = len(indices_response.json()) if indices_response.status_code == 200 else 0

        return {
            "elasticsearch_status": "healthy",
            "cluster_status": health_data.get("status", "unknown"),
            "ecs_indices_count": indices_count,
            "cluster_name": health_data.get("cluster_name", "unknown"),
            "number_of_nodes": health_data.get("number_of_nodes", 0)
        }

    except requests.RequestException as e:
        logger.error(f"Elasticsearch health check failed: {e}")
        return {
            "elasticsearch_status": "unhealthy",
            "error": str(e),
            "ecs_indices_count": 0
        }
    except Exception as e:
        logger.error(f"ECS health check failed: {e}")
        return {
            "elasticsearch_status": "error",
            "error": str(e),
            "ecs_indices_count": 0
        }
