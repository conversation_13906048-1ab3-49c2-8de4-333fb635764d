"""
Enhanced Logging Infrastructure for TurdParty
Provides structured JSON logging with correlation IDs and ELK integration
"""

import json
import logging
import time
import uuid
from datetime import datetime
from typing import Any, Dict, Optional, Union
from contextvars import ContextVar
from functools import wraps

import structlog
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware


# Context variable for correlation ID
correlation_id_var: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)


class CorrelationIdFilter(logging.Filter):
    """
    Logging filter that adds correlation ID to log records.
    
    This filter ensures that all log messages include the current
    correlation ID for request tracing across services.
    """
    
    def filter(self, record: logging.LogRecord) -> bool:
        """
        Add correlation ID to the log record.
        
        Args:
            record: The log record to filter
            
        Returns:
            bool: Always True to allow the record through
        """
        record.correlation_id = correlation_id_var.get() or "no-correlation"
        return True


class TurdPartyJSONFormatter(logging.Formatter):
    """
    Custom JSON formatter for TurdParty logs.
    
    Formats log records as structured JSON with consistent fields
    for better parsing and analysis in ELK stack.
    """
    
    def __init__(self, service_name: str = "turdparty-api") -> None:
        """
        Initialize the JSON formatter.
        
        Args:
            service_name: Name of the service generating logs
        """
        super().__init__()
        self.service_name = service_name
    
    def format(self, record: logging.LogRecord) -> str:
        """
        Format a log record as JSON.
        
        Args:
            record: The log record to format
            
        Returns:
            str: JSON-formatted log message
        """
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "service": self.service_name,
            "logger": record.name,
            "message": record.getMessage(),
            "correlation_id": getattr(record, 'correlation_id', 'no-correlation'),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # Add exception information if present
        if record.exc_info:
            log_entry["exception"] = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": self.formatException(record.exc_info)
            }
        
        # Add extra fields from the record
        for key, value in record.__dict__.items():
            if key not in {
                'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                'filename', 'module', 'exc_info', 'exc_text', 'stack_info',
                'lineno', 'funcName', 'created', 'msecs', 'relativeCreated',
                'thread', 'threadName', 'processName', 'process', 'getMessage',
                'correlation_id'
            }:
                log_entry[f"extra_{key}"] = value
        
        return json.dumps(log_entry, default=str)


class CorrelationIdMiddleware:
    """
    ASGI Middleware to generate and manage correlation IDs for requests.

    Generates a unique correlation ID for each request and makes it
    available throughout the request lifecycle for logging and tracing.

    Uses pure ASGI interface to properly handle both HTTP and WebSocket connections.
    """

    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        """
        ASGI interface for handling requests.

        Args:
            scope: ASGI scope dict
            receive: ASGI receive callable
            send: ASGI send callable
        """
        # Only process HTTP requests, pass through WebSocket and other types
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return

        # Generate correlation ID for HTTP requests
        correlation_id = str(uuid.uuid4())
        correlation_id_var.set(correlation_id)

        # Add correlation ID to scope for access in route handlers
        scope["correlation_id"] = correlation_id

        # Process the request
        start_time = time.time()

        async def send_wrapper(message):
            # Add correlation ID to response headers
            if message["type"] == "http.response.start":
                headers = list(message.get("headers", []))
                headers.append([b"x-correlation-id", correlation_id.encode()])
                headers.append([b"x-process-time", str(time.time() - start_time).encode()])
                message["headers"] = headers
            await send(message)

        await self.app(scope, receive, send_wrapper)




class PerformanceLogger:
    """
    Logger for performance metrics and timing information.
    
    Provides decorators and context managers for measuring and
    logging performance metrics across the application.
    """
    
    def __init__(self, logger_name: str = "turdparty.performance") -> None:
        """
        Initialize the performance logger.
        
        Args:
            logger_name: Name of the logger to use
        """
        self.logger = logging.getLogger(logger_name)
    
    def log_operation(self, operation: str, duration: float, **kwargs: Any) -> None:
        """
        Log a performance metric.
        
        Args:
            operation: Name of the operation
            duration: Duration in seconds
            **kwargs: Additional metadata
        """
        self.logger.info(
            f"Performance metric: {operation}",
            extra={
                "operation": operation,
                "duration_seconds": duration,
                "duration_ms": duration * 1000,
                **kwargs
            }
        )
    
    def time_operation(self, operation: str, **metadata: Any):
        """
        Decorator to time function execution.
        
        Args:
            operation: Name of the operation
            **metadata: Additional metadata to log
            
        Returns:
            Decorator function
        """
        def decorator(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = await func(*args, **kwargs)
                    success = True
                    error = None
                except Exception as e:
                    success = False
                    error = str(e)
                    raise
                finally:
                    duration = time.time() - start_time
                    self.log_operation(
                        operation,
                        duration,
                        success=success,
                        error=error,
                        **metadata
                    )
                return result
            
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    success = True
                    error = None
                except Exception as e:
                    success = False
                    error = str(e)
                    raise
                finally:
                    duration = time.time() - start_time
                    self.log_operation(
                        operation,
                        duration,
                        success=success,
                        error=error,
                        **metadata
                    )
                return result
            
            # Return appropriate wrapper based on function type
            import asyncio
            if asyncio.iscoroutinefunction(func):
                return async_wrapper
            else:
                return sync_wrapper
        
        return decorator


def setup_logging(
    service_name: str = "turdparty-api",
    log_level: str = "INFO",
    enable_json: bool = True
) -> None:
    """
    Set up enhanced logging configuration for TurdParty.
    
    Args:
        service_name: Name of the service
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        enable_json: Whether to use JSON formatting
    """
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Remove existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create console handler
    console_handler = logging.StreamHandler()
    
    if enable_json:
        # Use JSON formatter
        formatter = TurdPartyJSONFormatter(service_name)
    else:
        # Use standard formatter for development
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(correlation_id)s - %(message)s'
        )
    
    console_handler.setFormatter(formatter)
    
    # Add correlation ID filter
    correlation_filter = CorrelationIdFilter()
    console_handler.addFilter(correlation_filter)
    
    # Add handler to root logger
    root_logger.addHandler(console_handler)
    
    # Configure specific loggers
    logging.getLogger("uvicorn.access").disabled = True  # Disable uvicorn access logs
    logging.getLogger("fastapi").setLevel(logging.WARNING)
    
    # Set up performance logger
    perf_logger = logging.getLogger("turdparty.performance")
    perf_logger.setLevel(logging.INFO)


def get_correlation_id() -> Optional[str]:
    """
    Get the current correlation ID.
    
    Returns:
        str: Current correlation ID or None if not set
    """
    return correlation_id_var.get()


def set_correlation_id(correlation_id: str) -> None:
    """
    Set the correlation ID for the current context.
    
    Args:
        correlation_id: The correlation ID to set
    """
    correlation_id_var.set(correlation_id)


# Global performance logger instance
performance_logger = PerformanceLogger()
