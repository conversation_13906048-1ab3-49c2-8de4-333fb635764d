"""
File Tree Monitor for TurdParty
Captures complete installation footprint with file locations and directory structure
"""

import os
import json
import hashlib
from datetime import datetime
from typing import Dict, List, Any
from pathlib import Path


class FileTreeMonitor:
    """Monitors and captures complete file tree changes during installation."""
    
    def __init__(self):
        self.baseline_snapshot = {}
        self.post_install_snapshot = {}
        
    def capture_baseline_snapshot(self, vm_connection) -> Dict[str, Any]:
        """Capture baseline file system state before installation."""
        try:
            # Capture critical directories
            directories_to_monitor = [
                "C:\\Program Files",
                "C:\\Program Files (x86)", 
                "C:\\Windows\\System32",
                "C:\\Users\\<USER>\\AppData",
                "C:\\ProgramData",
                "C:\\Windows\\Temp"
            ]
            
            baseline = {}
            for directory in directories_to_monitor:
                baseline[directory] = self._scan_directory_tree(vm_connection, directory)
            
            self.baseline_snapshot = baseline
            return baseline
            
        except Exception as e:
            print(f"Failed to capture baseline: {e}")
            return {}
    
    def capture_post_install_snapshot(self, vm_connection) -> Dict[str, Any]:
        """Capture file system state after installation."""
        try:
            directories_to_monitor = [
                "C:\\Program Files",
                "C:\\Program Files (x86)",
                "C:\\Windows\\System32", 
                "C:\\Users\\<USER>\\AppData",
                "C:\\ProgramData",
                "C:\\Windows\\Temp"
            ]
            
            post_install = {}
            for directory in directories_to_monitor:
                post_install[directory] = self._scan_directory_tree(vm_connection, directory)
            
            self.post_install_snapshot = post_install
            return post_install
            
        except Exception as e:
            print(f"Failed to capture post-install snapshot: {e}")
            return {}
    
    def _scan_directory_tree(self, vm_connection, directory_path: str) -> Dict[str, Any]:
        """Scan directory tree and return file metadata."""
        try:
            # PowerShell command to get detailed file information
            cmd = f'''
            powershell -Command "
            Get-ChildItem -Path '{directory_path}' -Recurse -Force -ErrorAction SilentlyContinue | 
            Where-Object {{$_.PSIsContainer -eq $false}} |
            Select-Object FullName, Length, CreationTime, LastWriteTime, LastAccessTime |
            ConvertTo-Json -Depth 3
            "
            '''
            
            stdin, stdout, stderr = vm_connection.exec_command(cmd)
            result = stdout.read().decode('utf-8', errors='ignore')
            
            if result.strip():
                files_data = json.loads(result)
                if not isinstance(files_data, list):
                    files_data = [files_data]
                
                # Convert to structured format
                file_tree = {}
                for file_info in files_data:
                    file_path = file_info.get('FullName', '')
                    file_tree[file_path] = {
                        'size': file_info.get('Length', 0),
                        'created': file_info.get('CreationTime', ''),
                        'modified': file_info.get('LastWriteTime', ''),
                        'accessed': file_info.get('LastAccessTime', '')
                    }
                
                return file_tree
            
            return {}
            
        except Exception as e:
            print(f"Failed to scan directory {directory_path}: {e}")
            return {}
    
    def generate_installation_footprint(self) -> Dict[str, Any]:
        """Generate complete installation footprint by comparing snapshots."""
        try:
            footprint = {
                'files_created': [],
                'files_modified': [],
                'files_deleted': [],
                'directories_created': [],
                'total_files_added': 0,
                'total_size_added': 0,
                'installation_paths': set()
            }
            
            # Find new files (in post-install but not in baseline)
            for directory, post_files in self.post_install_snapshot.items():
                baseline_files = self.baseline_snapshot.get(directory, {})
                
                for file_path, file_info in post_files.items():
                    if file_path not in baseline_files:
                        # New file created
                        footprint['files_created'].append({
                            'path': file_path,
                            'size': file_info['size'],
                            'created': file_info['created'],
                            'directory': directory
                        })
                        footprint['total_files_added'] += 1
                        footprint['total_size_added'] += file_info['size']
                        
                        # Track installation paths
                        install_dir = str(Path(file_path).parent)
                        footprint['installation_paths'].add(install_dir)
                    
                    elif baseline_files[file_path]['modified'] != file_info['modified']:
                        # File was modified
                        footprint['files_modified'].append({
                            'path': file_path,
                            'size': file_info['size'],
                            'modified': file_info['modified'],
                            'directory': directory
                        })
            
            # Find deleted files (in baseline but not in post-install)
            for directory, baseline_files in self.baseline_snapshot.items():
                post_files = self.post_install_snapshot.get(directory, {})
                
                for file_path in baseline_files:
                    if file_path not in post_files:
                        footprint['files_deleted'].append({
                            'path': file_path,
                            'directory': directory
                        })
            
            # Convert set to list for JSON serialization
            footprint['installation_paths'] = list(footprint['installation_paths'])
            
            return footprint
            
        except Exception as e:
            print(f"Failed to generate installation footprint: {e}")
            return {}
    
    def generate_ecs_file_events(self, footprint: Dict[str, Any], vm_id: str, file_uuid: str) -> List[Dict[str, Any]]:
        """Generate ECS-compliant file events from installation footprint."""
        events = []
        
        try:
            # File creation events
            for file_info in footprint.get('files_created', []):
                event = {
                    "@timestamp": datetime.utcnow().isoformat() + "Z",
                    "ecs": {"version": "8.11.0"},
                    "event": {
                        "kind": "event",
                        "category": ["file"],
                        "type": ["creation"],
                        "action": "file_created",
                        "outcome": "success"
                    },
                    "file": {
                        "path": file_info['path'],
                        "size": file_info['size'],
                        "created": file_info['created'],
                        "type": "file"
                    },
                    "host": {
                        "name": vm_id,
                        "id": vm_id
                    },
                    "turdparty": {
                        "file_uuid": file_uuid,
                        "vm_id": vm_id,
                        "phase": "installation",
                        "directory": file_info['directory']
                    },
                    "tags": ["file-creation", "installation", "malware-analysis"]
                }
                events.append(event)
            
            # File modification events
            for file_info in footprint.get('files_modified', []):
                event = {
                    "@timestamp": datetime.utcnow().isoformat() + "Z",
                    "ecs": {"version": "8.11.0"},
                    "event": {
                        "kind": "event",
                        "category": ["file"],
                        "type": ["change"],
                        "action": "file_modified",
                        "outcome": "success"
                    },
                    "file": {
                        "path": file_info['path'],
                        "size": file_info['size'],
                        "modified": file_info['modified'],
                        "type": "file"
                    },
                    "host": {
                        "name": vm_id,
                        "id": vm_id
                    },
                    "turdparty": {
                        "file_uuid": file_uuid,
                        "vm_id": vm_id,
                        "phase": "installation",
                        "directory": file_info['directory']
                    },
                    "tags": ["file-modification", "installation", "malware-analysis"]
                }
                events.append(event)
            
            return events
            
        except Exception as e:
            print(f"Failed to generate ECS file events: {e}")
            return []
