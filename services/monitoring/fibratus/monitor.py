"""
VM Monitoring Agent for TurdParty
Monitors Docker containers (VMs) and sends ECS-compliant data to Elasticsearch

Implements dual logging:
1. Install-time UUID ECS logging (file injection, setup)
2. Runtime UUID ECS logging (post-install execution monitoring)
"""

import json
import logging
import os
import time
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, List

import docker
import psutil
import requests
from elasticsearch import Elasticsearch
from pythonjsonlogger import jsonlogger

# Configuration
ELASTICSEARCH_HOST = os.getenv("ELASTICSEARCH_HOST", "elasticsearch")
ELASTICSEARCH_PORT = int(os.getenv("ELASTICSEARCH_PORT", "9200"))
LOGSTASH_HOST = os.getenv("LOGSTASH_HOST", "logstash")
LOGSTASH_PORT = int(os.getenv("LOGSTASH_PORT", "5044"))
MONITOR_INTERVAL = int(os.getenv("MONITOR_INTERVAL", "10"))

# ECS Index Configuration
INSTALL_INDEX_PREFIX = "turdparty-install-ecs"
RUNTIME_INDEX_PREFIX = "turdparty-runtime-ecs"

# Setup logging
logger = logging.getLogger(__name__)
logHandler = logging.StreamHandler()
formatter = jsonlogger.JsonFormatter()
logHandler.setFormatter(formatter)
logger.addHandler(logHandler)
logger.setLevel(logging.INFO)


class TurdPartyVMMonitor:
    """VM monitoring agent with dual ECS logging."""
    
    def __init__(self):
        self.docker_client = docker.from_env()
        self.es_client = None
        self.setup_elasticsearch()
        
    def setup_elasticsearch(self):
        """Setup Elasticsearch client and create ECS indices."""
        try:
            self.es_client = Elasticsearch(
                [f"http://{ELASTICSEARCH_HOST}:{ELASTICSEARCH_PORT}"],
                timeout=30,
                max_retries=3,
                retry_on_timeout=True
            )
            
            # Wait for Elasticsearch to be ready
            self._wait_for_elasticsearch()
            
            # Create ECS index templates and indices
            self._create_ecs_index_templates()
            self._create_ecs_indices()
            
            logger.info("Elasticsearch setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup Elasticsearch: {e}")
            raise
    
    def _wait_for_elasticsearch(self, max_retries=30):
        """Wait for Elasticsearch to be available."""
        for attempt in range(max_retries):
            try:
                if self.es_client.ping():
                    logger.info("Elasticsearch is ready")
                    return
            except Exception as e:
                logger.warning(f"Elasticsearch not ready (attempt {attempt + 1}): {e}")
                time.sleep(10)
        
        raise RuntimeError("Elasticsearch not available after maximum retries")
    
    def _create_ecs_index_templates(self):
        """Create ECS-compliant index templates for install and runtime data."""
        
        # Install-time ECS template
        install_template = {
            "index_patterns": [f"{INSTALL_INDEX_PREFIX}-*"],
            "template": {
                "settings": {
                    "number_of_shards": 1,
                    "number_of_replicas": 0,
                    "refresh_interval": "5s",
                    "index.lifecycle.name": "turdparty-install-policy"
                },
                "mappings": {
                    "properties": {
                        "@timestamp": {"type": "date"},
                        "ecs": {
                            "properties": {
                                "version": {"type": "keyword"}
                            }
                        },
                        "event": {
                            "properties": {
                                "kind": {"type": "keyword"},
                                "category": {"type": "keyword"},
                                "type": {"type": "keyword"},
                                "action": {"type": "keyword"},
                                "outcome": {"type": "keyword"},
                                "duration": {"type": "long"}
                            }
                        },
                        "turdparty": {
                            "properties": {
                                "workflow_id": {"type": "keyword"},
                                "file_id": {"type": "keyword"},
                                "vm_id": {"type": "keyword"},
                                "phase": {"type": "keyword"},
                                "install_uuid": {"type": "keyword"}
                            }
                        },
                        "file": {
                            "properties": {
                                "name": {"type": "keyword"},
                                "path": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
                                "size": {"type": "long"},
                                "hash": {
                                    "properties": {
                                        "sha256": {"type": "keyword"}
                                    }
                                }
                            }
                        },
                        "process": {
                            "properties": {
                                "pid": {"type": "long"},
                                "name": {"type": "keyword"},
                                "command_line": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
                                "executable": {"type": "keyword"}
                            }
                        },
                        "host": {
                            "properties": {
                                "name": {"type": "keyword"},
                                "id": {"type": "keyword"},
                                "ip": {"type": "ip"}
                            }
                        },
                        "message": {"type": "text"}
                    }
                }
            }
        }
        
        # Runtime ECS template
        runtime_template = {
            "index_patterns": [f"{RUNTIME_INDEX_PREFIX}-*"],
            "template": {
                "settings": {
                    "number_of_shards": 1,
                    "number_of_replicas": 0,
                    "refresh_interval": "5s",
                    "index.lifecycle.name": "turdparty-runtime-policy"
                },
                "mappings": {
                    "properties": {
                        "@timestamp": {"type": "date"},
                        "ecs": {
                            "properties": {
                                "version": {"type": "keyword"}
                            }
                        },
                        "event": {
                            "properties": {
                                "kind": {"type": "keyword"},
                                "category": {"type": "keyword"},
                                "type": {"type": "keyword"},
                                "action": {"type": "keyword"},
                                "outcome": {"type": "keyword"},
                                "duration": {"type": "long"}
                            }
                        },
                        "turdparty": {
                            "properties": {
                                "workflow_id": {"type": "keyword"},
                                "file_id": {"type": "keyword"},
                                "vm_id": {"type": "keyword"},
                                "phase": {"type": "keyword"},
                                "runtime_uuid": {"type": "keyword"}
                            }
                        },
                        "file": {
                            "properties": {
                                "name": {"type": "keyword"},
                                "path": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
                                "accessed": {"type": "date"},
                                "created": {"type": "date"},
                                "modified": {"type": "date"}
                            }
                        },
                        "process": {
                            "properties": {
                                "pid": {"type": "long"},
                                "ppid": {"type": "long"},
                                "name": {"type": "keyword"},
                                "command_line": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
                                "executable": {"type": "keyword"},
                                "start": {"type": "date"},
                                "working_directory": {"type": "keyword"}
                            }
                        },
                        "network": {
                            "properties": {
                                "protocol": {"type": "keyword"},
                                "direction": {"type": "keyword"},
                                "bytes": {"type": "long"},
                                "packets": {"type": "long"}
                            }
                        },
                        "source": {
                            "properties": {
                                "ip": {"type": "ip"},
                                "port": {"type": "long"}
                            }
                        },
                        "destination": {
                            "properties": {
                                "ip": {"type": "ip"},
                                "port": {"type": "long"}
                            }
                        },
                        "host": {
                            "properties": {
                                "name": {"type": "keyword"},
                                "id": {"type": "keyword"},
                                "ip": {"type": "ip"},
                                "cpu": {
                                    "properties": {
                                        "usage": {"type": "float"}
                                    }
                                },
                                "memory": {
                                    "properties": {
                                        "usage": {"type": "float"},
                                        "total": {"type": "long"},
                                        "available": {"type": "long"}
                                    }
                                }
                            }
                        },
                        "message": {"type": "text"}
                    }
                }
            }
        }
        
        # Create templates
        try:
            self.es_client.indices.put_index_template(
                name="turdparty-install-ecs-template",
                body=install_template
            )
            logger.info("Created install-time ECS index template")
            
            self.es_client.indices.put_index_template(
                name="turdparty-runtime-ecs-template", 
                body=runtime_template
            )
            logger.info("Created runtime ECS index template")
            
        except Exception as e:
            logger.error(f"Failed to create ECS index templates: {e}")
            raise
    
    def _create_ecs_indices(self):
        """Create initial ECS indices."""
        today = datetime.now().strftime("%Y.%m.%d")
        
        install_index = f"{INSTALL_INDEX_PREFIX}-{today}"
        runtime_index = f"{RUNTIME_INDEX_PREFIX}-{today}"
        
        for index_name in [install_index, runtime_index]:
            try:
                if not self.es_client.indices.exists(index=index_name):
                    self.es_client.indices.create(index=index_name)
                    logger.info(f"Created ECS index: {index_name}")
            except Exception as e:
                logger.error(f"Failed to create index {index_name}: {e}")
    
    def log_install_event(self, workflow_id: str, vm_id: str, file_id: str, event_data: Dict[str, Any]):
        """Log install-time events with UUID to install ECS index."""
        install_uuid = str(uuid.uuid4())
        
        ecs_event = {
            "@timestamp": datetime.now(timezone.utc).isoformat(),
            "ecs": {"version": "8.11.0"},
            "event": {
                "kind": "event",
                "category": ["file", "process"],
                "type": ["installation", "change"],
                "action": event_data.get("action", "file_injection"),
                "outcome": event_data.get("outcome", "success")
            },
            "turdparty": {
                "workflow_id": workflow_id,
                "file_id": file_id,
                "vm_id": vm_id,
                "phase": "install",
                "install_uuid": install_uuid
            },
            "file": event_data.get("file", {}),
            "process": event_data.get("process", {}),
            "host": event_data.get("host", {}),
            "message": event_data.get("message", "Install-time event")
        }
        
        self._send_to_elasticsearch(ecs_event, INSTALL_INDEX_PREFIX)
        return install_uuid
    
    def log_runtime_event(self, workflow_id: str, vm_id: str, file_id: str, event_data: Dict[str, Any]):
        """Log runtime events with UUID to runtime ECS index."""
        runtime_uuid = str(uuid.uuid4())
        
        ecs_event = {
            "@timestamp": datetime.now(timezone.utc).isoformat(),
            "ecs": {"version": "8.11.0"},
            "event": {
                "kind": "event",
                "category": event_data.get("categories", ["process"]),
                "type": event_data.get("types", ["start"]),
                "action": event_data.get("action", "process_execution"),
                "outcome": event_data.get("outcome", "success")
            },
            "turdparty": {
                "workflow_id": workflow_id,
                "file_id": file_id,
                "vm_id": vm_id,
                "phase": "runtime",
                "runtime_uuid": runtime_uuid
            },
            "file": event_data.get("file", {}),
            "process": event_data.get("process", {}),
            "network": event_data.get("network", {}),
            "source": event_data.get("source", {}),
            "destination": event_data.get("destination", {}),
            "host": event_data.get("host", {}),
            "message": event_data.get("message", "Runtime event")
        }
        
        self._send_to_elasticsearch(ecs_event, RUNTIME_INDEX_PREFIX)
        return runtime_uuid
    
    def _send_to_elasticsearch(self, event: Dict[str, Any], index_prefix: str):
        """Send event to appropriate Elasticsearch index."""
        today = datetime.now().strftime("%Y.%m.%d")
        index_name = f"{index_prefix}-{today}"
        
        try:
            self.es_client.index(index=index_name, body=event)
        except Exception as e:
            logger.error(f"Failed to send event to Elasticsearch: {e}")
    
    def monitor_turdparty_vms(self):
        """Monitor TurdParty VMs and generate ECS events."""
        logger.info("Starting TurdParty VM monitoring...")
        
        while True:
            try:
                # Get all TurdParty VM containers
                containers = self.docker_client.containers.list(
                    filters={"name": "turdparty-vm-"}
                )
                
                for container in containers:
                    self._monitor_container(container)
                
                time.sleep(MONITOR_INTERVAL)
                
            except Exception as e:
                logger.error(f"Monitoring error: {e}")
                time.sleep(30)
    
    def _monitor_container(self, container):
        """Monitor a specific container and generate events."""
        try:
            # Extract workflow info from container name
            container_name = container.name
            if not container_name.startswith("turdparty-vm-"):
                return
            
            workflow_id = container_name.replace("turdparty-vm-", "")
            vm_id = container.id[:12]
            
            # Get container stats
            stats = container.stats(stream=False)
            
            # Generate runtime monitoring event
            runtime_event = {
                "action": "system_monitoring",
                "categories": ["host"],
                "types": ["info"],
                "host": {
                    "name": container_name,
                    "id": vm_id,
                    "cpu": {
                        "usage": self._calculate_cpu_usage(stats)
                    },
                    "memory": {
                        "usage": self._calculate_memory_usage(stats),
                        "total": stats.get("memory_stats", {}).get("limit", 0),
                        "available": stats.get("memory_stats", {}).get("limit", 0) - stats.get("memory_stats", {}).get("usage", 0)
                    }
                },
                "message": f"System monitoring for VM {container_name}"
            }
            
            self.log_runtime_event(workflow_id, vm_id, "unknown", runtime_event)
            
        except Exception as e:
            logger.error(f"Container monitoring error: {e}")
    
    def _calculate_cpu_usage(self, stats: Dict) -> float:
        """Calculate CPU usage percentage from Docker stats."""
        try:
            cpu_stats = stats.get("cpu_stats", {})
            precpu_stats = stats.get("precpu_stats", {})
            
            cpu_usage = cpu_stats.get("cpu_usage", {})
            precpu_usage = precpu_stats.get("cpu_usage", {})
            
            cpu_delta = cpu_usage.get("total_usage", 0) - precpu_usage.get("total_usage", 0)
            system_delta = cpu_stats.get("system_cpu_usage", 0) - precpu_stats.get("system_cpu_usage", 0)
            
            if system_delta > 0 and cpu_delta > 0:
                cpu_percent = (cpu_delta / system_delta) * len(cpu_usage.get("percpu_usage", [1])) * 100.0
                return round(cpu_percent, 2)
            
            return 0.0
            
        except Exception:
            return 0.0
    
    def _calculate_memory_usage(self, stats: Dict) -> float:
        """Calculate memory usage percentage from Docker stats."""
        try:
            memory_stats = stats.get("memory_stats", {})
            usage = memory_stats.get("usage", 0)
            limit = memory_stats.get("limit", 1)
            
            if limit > 0:
                memory_percent = (usage / limit) * 100.0
                return round(memory_percent, 2)
            
            return 0.0
            
        except Exception:
            return 0.0


def main():
    """Main monitoring loop."""
    logger.info("Starting TurdParty VM Monitor with dual ECS logging")
    
    monitor = TurdPartyVMMonitor()
    
    # Example install event (would be called from injection tasks)
    # install_uuid = monitor.log_install_event(
    #     "test-workflow", "test-vm", "test-file",
    #     {
    #         "action": "file_injection",
    #         "file": {"name": "test.sh", "path": "/tmp/test.sh"},
    #         "message": "File injected into VM"
    #     }
    # )
    
    # Start monitoring
    monitor.monitor_turdparty_vms()


class NetworkTrafficMonitor:
    """Monitors network traffic during installation and runtime."""

    def __init__(self):
        self.baseline_connections = {}
        self.active_connections = {}

    def capture_network_baseline(self, vm_connection) -> Dict[str, Any]:
        """Capture baseline network connections before installation."""
        try:
            cmd = '''
            powershell -Command "
            Get-NetTCPConnection | Select-Object LocalAddress, LocalPort, RemoteAddress, RemotePort, State, OwningProcess |
            ConvertTo-Json -Depth 2
            "
            '''

            stdin, stdout, stderr = vm_connection.exec_command(cmd)
            result = stdout.read().decode('utf-8', errors='ignore')

            if result.strip():
                connections = json.loads(result)
                if not isinstance(connections, list):
                    connections = [connections]

                self.baseline_connections = {
                    f"{conn.get('LocalAddress')}:{conn.get('LocalPort')}-{conn.get('RemoteAddress')}:{conn.get('RemotePort')}": conn
                    for conn in connections
                }

            return self.baseline_connections

        except Exception as e:
            print(f"Failed to capture network baseline: {e}")
            return {}

    def monitor_network_traffic(self, vm_connection, duration_seconds: int = 300) -> List[Dict[str, Any]]:
        """Monitor network traffic during installation."""
        try:
            # Monitor network connections for specified duration
            cmd = f'''
            powershell -Command "
            $startTime = Get-Date
            $endTime = $startTime.AddSeconds({duration_seconds})
            $connections = @()

            while ((Get-Date) -lt $endTime) {{
                $current = Get-NetTCPConnection | Select-Object LocalAddress, LocalPort, RemoteAddress, RemotePort, State, OwningProcess
                $connections += $current
                Start-Sleep -Seconds 5
            }}

            $connections | ConvertTo-Json -Depth 2
            "
            '''

            stdin, stdout, stderr = vm_connection.exec_command(cmd)
            result = stdout.read().decode('utf-8', errors='ignore')

            if result.strip():
                all_connections = json.loads(result)
                if not isinstance(all_connections, list):
                    all_connections = [all_connections]

                # Filter for new connections not in baseline
                new_connections = []
                for conn in all_connections:
                    conn_key = f"{conn.get('LocalAddress')}:{conn.get('LocalPort')}-{conn.get('RemoteAddress')}:{conn.get('RemotePort')}"
                    if conn_key not in self.baseline_connections:
                        new_connections.append(conn)

                return new_connections

            return []

        except Exception as e:
            print(f"Failed to monitor network traffic: {e}")
            return []

    def generate_ecs_network_events(self, connections: List[Dict[str, Any]], vm_id: str, file_uuid: str) -> List[Dict[str, Any]]:
        """Generate ECS-compliant network events."""
        events = []

        try:
            for conn in connections:
                event = {
                    "@timestamp": datetime.now(timezone.utc).isoformat(),
                    "ecs": {"version": "8.11.0"},
                    "event": {
                        "kind": "event",
                        "category": ["network"],
                        "type": ["connection"],
                        "action": "network_connection_established",
                        "outcome": "success"
                    },
                    "network": {
                        "protocol": "tcp",
                        "direction": "outbound" if conn.get('State') == 'Established' else "unknown"
                    },
                    "source": {
                        "ip": conn.get('LocalAddress'),
                        "port": conn.get('LocalPort')
                    },
                    "destination": {
                        "ip": conn.get('RemoteAddress'),
                        "port": conn.get('RemotePort')
                    },
                    "process": {
                        "pid": conn.get('OwningProcess')
                    },
                    "host": {
                        "name": vm_id,
                        "id": vm_id
                    },
                    "turdparty": {
                        "file_uuid": file_uuid,
                        "vm_id": vm_id,
                        "phase": "installation"
                    },
                    "tags": ["network-traffic", "installation", "malware-analysis"]
                }
                events.append(event)

            return events

        except Exception as e:
            print(f"Failed to generate ECS network events: {e}")
            return []


if __name__ == "__main__":
    main()
