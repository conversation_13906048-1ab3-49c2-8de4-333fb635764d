"""
Registry Monitor for TurdParty
Captures detailed registry changes during malware installation
"""

import json
import re
from datetime import datetime
from typing import Dict, List, Any


class RegistryMonitor:
    """Monitors registry changes during installation with detailed per-file tracking."""
    
    def __init__(self):
        self.baseline_registry = {}
        self.post_install_registry = {}
        
    def capture_baseline_registry(self, vm_connection) -> Dict[str, Any]:
        """Capture baseline registry state before installation."""
        try:
            # Key registry hives to monitor
            registry_hives = [
                "HKLM:\\SOFTWARE",
                "HKLM:\\SYSTEM\\CurrentControlSet\\Services",
                "HKCU:\\SOFTWARE",
                "HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run",
                "HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall"
            ]
            
            baseline = {}
            for hive in registry_hives:
                baseline[hive] = self._scan_registry_hive(vm_connection, hive)
            
            self.baseline_registry = baseline
            return baseline
            
        except Exception as e:
            print(f"Failed to capture baseline registry: {e}")
            return {}
    
    def capture_post_install_registry(self, vm_connection) -> Dict[str, Any]:
        """Capture registry state after installation."""
        try:
            registry_hives = [
                "HKLM:\\SOFTWARE",
                "HKLM:\\SYSTEM\\CurrentControlSet\\Services", 
                "HKCU:\\SOFTWARE",
                "HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run",
                "HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall"
            ]
            
            post_install = {}
            for hive in registry_hives:
                post_install[hive] = self._scan_registry_hive(vm_connection, hive)
            
            self.post_install_registry = post_install
            return post_install
            
        except Exception as e:
            print(f"Failed to capture post-install registry: {e}")
            return {}
    
    def _scan_registry_hive(self, vm_connection, hive_path: str) -> Dict[str, Any]:
        """Scan registry hive and return key/value data."""
        try:
            # PowerShell command to get detailed registry information
            cmd = f'''
            powershell -Command "
            Get-ChildItem -Path '{hive_path}' -Recurse -ErrorAction SilentlyContinue |
            ForEach-Object {{
                $keyPath = $_.PSPath
                try {{
                    $values = Get-ItemProperty -Path $keyPath -ErrorAction SilentlyContinue
                    if ($values) {{
                        $valueData = @{{}}
                        $values.PSObject.Properties | ForEach-Object {{
                            if ($_.Name -notmatch '^PS') {{
                                $valueData[$_.Name] = $_.Value
                            }}
                        }}
                        [PSCustomObject]@{{
                            KeyPath = $keyPath
                            Values = $valueData
                        }}
                    }}
                }} catch {{}}
            }} | ConvertTo-Json -Depth 4
            "
            '''
            
            stdin, stdout, stderr = vm_connection.exec_command(cmd)
            result = stdout.read().decode('utf-8', errors='ignore')
            
            if result.strip():
                registry_data = json.loads(result)
                if not isinstance(registry_data, list):
                    registry_data = [registry_data]
                
                # Convert to structured format
                registry_tree = {}
                for reg_info in registry_data:
                    key_path = reg_info.get('KeyPath', '')
                    values = reg_info.get('Values', {})
                    registry_tree[key_path] = values
                
                return registry_tree
            
            return {}
            
        except Exception as e:
            print(f"Failed to scan registry hive {hive_path}: {e}")
            return {}
    
    def generate_registry_changes(self) -> Dict[str, Any]:
        """Generate detailed registry changes by comparing snapshots."""
        try:
            changes = {
                'keys_created': [],
                'keys_modified': [],
                'keys_deleted': [],
                'values_created': [],
                'values_modified': [],
                'values_deleted': [],
                'total_keys_added': 0,
                'total_values_added': 0
            }
            
            # Find new registry keys and values
            for hive, post_keys in self.post_install_registry.items():
                baseline_keys = self.baseline_registry.get(hive, {})
                
                for key_path, post_values in post_keys.items():
                    if key_path not in baseline_keys:
                        # New registry key created
                        changes['keys_created'].append({
                            'key_path': key_path,
                            'hive': hive,
                            'values': post_values,
                            'timestamp': datetime.utcnow().isoformat()
                        })
                        changes['total_keys_added'] += 1
                        
                        # Count new values
                        for value_name, value_data in post_values.items():
                            changes['values_created'].append({
                                'key_path': key_path,
                                'value_name': value_name,
                                'value_data': value_data,
                                'hive': hive
                            })
                            changes['total_values_added'] += 1
                    
                    else:
                        # Check for modified values in existing keys
                        baseline_values = baseline_keys[key_path]
                        
                        for value_name, post_value in post_values.items():
                            if value_name not in baseline_values:
                                # New value in existing key
                                changes['values_created'].append({
                                    'key_path': key_path,
                                    'value_name': value_name,
                                    'value_data': post_value,
                                    'hive': hive
                                })
                                changes['total_values_added'] += 1
                            
                            elif baseline_values[value_name] != post_value:
                                # Value was modified
                                changes['values_modified'].append({
                                    'key_path': key_path,
                                    'value_name': value_name,
                                    'old_value': baseline_values[value_name],
                                    'new_value': post_value,
                                    'hive': hive
                                })
            
            # Find deleted keys and values
            for hive, baseline_keys in self.baseline_registry.items():
                post_keys = self.post_install_registry.get(hive, {})
                
                for key_path, baseline_values in baseline_keys.items():
                    if key_path not in post_keys:
                        # Registry key was deleted
                        changes['keys_deleted'].append({
                            'key_path': key_path,
                            'hive': hive,
                            'values': baseline_values
                        })
                    else:
                        # Check for deleted values
                        post_values = post_keys[key_path]
                        for value_name, baseline_value in baseline_values.items():
                            if value_name not in post_values:
                                changes['values_deleted'].append({
                                    'key_path': key_path,
                                    'value_name': value_name,
                                    'value_data': baseline_value,
                                    'hive': hive
                                })
            
            return changes
            
        except Exception as e:
            print(f"Failed to generate registry changes: {e}")
            return {}
    
    def generate_ecs_registry_events(self, changes: Dict[str, Any], vm_id: str, file_uuid: str) -> List[Dict[str, Any]]:
        """Generate ECS-compliant registry events from changes."""
        events = []
        
        try:
            # Registry key creation events
            for key_info in changes.get('keys_created', []):
                event = {
                    "@timestamp": datetime.utcnow().isoformat() + "Z",
                    "ecs": {"version": "8.11.0"},
                    "event": {
                        "kind": "event",
                        "category": ["configuration"],
                        "type": ["creation"],
                        "action": "registry_key_created",
                        "outcome": "success"
                    },
                    "registry": {
                        "key": key_info['key_path'],
                        "hive": key_info['hive'],
                        "values": key_info['values']
                    },
                    "host": {
                        "name": vm_id,
                        "id": vm_id
                    },
                    "turdparty": {
                        "file_uuid": file_uuid,
                        "vm_id": vm_id,
                        "phase": "installation"
                    },
                    "tags": ["registry-creation", "installation", "malware-analysis"]
                }
                events.append(event)
            
            # Registry value creation events
            for value_info in changes.get('values_created', []):
                event = {
                    "@timestamp": datetime.utcnow().isoformat() + "Z",
                    "ecs": {"version": "8.11.0"},
                    "event": {
                        "kind": "event",
                        "category": ["configuration"],
                        "type": ["creation"],
                        "action": "registry_value_created",
                        "outcome": "success"
                    },
                    "registry": {
                        "key": value_info['key_path'],
                        "value": value_info['value_name'],
                        "data": value_info['value_data'],
                        "hive": value_info['hive']
                    },
                    "host": {
                        "name": vm_id,
                        "id": vm_id
                    },
                    "turdparty": {
                        "file_uuid": file_uuid,
                        "vm_id": vm_id,
                        "phase": "installation"
                    },
                    "tags": ["registry-value-creation", "installation", "malware-analysis"]
                }
                events.append(event)
            
            # Registry value modification events
            for value_info in changes.get('values_modified', []):
                event = {
                    "@timestamp": datetime.utcnow().isoformat() + "Z",
                    "ecs": {"version": "8.11.0"},
                    "event": {
                        "kind": "event",
                        "category": ["configuration"],
                        "type": ["change"],
                        "action": "registry_value_modified",
                        "outcome": "success"
                    },
                    "registry": {
                        "key": value_info['key_path'],
                        "value": value_info['value_name'],
                        "data": {
                            "old": value_info['old_value'],
                            "new": value_info['new_value']
                        },
                        "hive": value_info['hive']
                    },
                    "host": {
                        "name": vm_id,
                        "id": vm_id
                    },
                    "turdparty": {
                        "file_uuid": file_uuid,
                        "vm_id": vm_id,
                        "phase": "installation"
                    },
                    "tags": ["registry-value-modification", "installation", "malware-analysis"]
                }
                events.append(event)
            
            return events
            
        except Exception as e:
            print(f"Failed to generate ECS registry events: {e}")
            return []
