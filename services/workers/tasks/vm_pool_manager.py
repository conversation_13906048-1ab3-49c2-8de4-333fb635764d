"""VM Pool Management for TurdParty workflow."""

import logging
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from uuid import uuid4

from celery import shared_task
from celery.utils.log import get_task_logger
from sqlalchemy import text
from sqlalchemy.orm import sessionmaker

# Database setup for workers
from sqlalchemy import create_engine
DATABASE_URL = "**********************************************/turdparty"
engine = create_engine(DATABASE_URL)

# Import models
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from tasks.models import VMStatus
from tasks.vm_management import create_vm

logger = get_task_logger(__name__)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


class VMPoolManager:
    """Manages a pool of ready VMs for file processing."""
    
    def __init__(self, pool_config: Dict[str, Any]):
        self.pool_config = pool_config
        self.min_pool_size = pool_config.get("min_pool_size", 2)
        self.max_pool_size = pool_config.get("max_pool_size", 10)
        self.vm_templates = pool_config.get("templates", ["ubuntu:20.04", "ubuntu:22.04"])
        self.default_vm_config = pool_config.get("default_vm_config", {
            "memory_mb": 1024,
            "cpus": 1,
            "disk_gb": 20
        })
    
    def get_pool_status(self) -> Dict[str, Any]:
        """Get current pool status."""
        with SessionLocal() as db:
            # Count VMs by status
            ready_count = db.execute(
                text("SELECT COUNT(*) FROM vm_instances WHERE status = :status"),
                {"status": VMStatus.READY.value}
            ).scalar()
            
            creating_count = db.execute(
                text("SELECT COUNT(*) FROM vm_instances WHERE status = :status"),
                {"status": VMStatus.CREATING.value}
            ).scalar()
            
            running_count = db.execute(
                text("SELECT COUNT(*) FROM vm_instances WHERE status = :status"),
                {"status": VMStatus.RUNNING.value}
            ).scalar()
            
            total_count = db.execute(
                text("SELECT COUNT(*) FROM vm_instances WHERE status NOT IN (:terminated, :failed)"),
                {"terminated": VMStatus.TERMINATED.value, "failed": VMStatus.FAILED.value}
            ).scalar()
            
            return {
                "ready": ready_count,
                "creating": creating_count,
                "running": running_count,
                "total": total_count,
                "min_pool_size": self.min_pool_size,
                "max_pool_size": self.max_pool_size,
                "needs_provisioning": ready_count < self.min_pool_size
            }
    
    def get_ready_vm(self, template: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get a ready VM from the pool."""
        with SessionLocal() as db:
            query = text("""
                SELECT id, name, vm_id, template, memory_mb, cpus, ip_address, ssh_port
                FROM vm_instances 
                WHERE status = :status
            """)
            
            params = {"status": VMStatus.READY.value}
            
            if template:
                query = text("""
                    SELECT id, name, vm_id, template, memory_mb, cpus, ip_address, ssh_port
                    FROM vm_instances 
                    WHERE status = :status AND template = :template
                    ORDER BY created_at ASC
                    LIMIT 1
                """)
                params["template"] = template
            else:
                query = text("""
                    SELECT id, name, vm_id, template, memory_mb, cpus, ip_address, ssh_port
                    FROM vm_instances 
                    WHERE status = :status
                    ORDER BY created_at ASC
                    LIMIT 1
                """)
            
            result = db.execute(query, params).fetchone()
            
            if result:
                # Mark VM as allocated
                db.execute(
                    text("UPDATE vm_instances SET status = :status WHERE id = :vm_id"),
                    {"status": VMStatus.RUNNING.value, "vm_id": result.id}
                )
                db.commit()
                
                return {
                    "id": str(result.id),
                    "name": result.name,
                    "vm_id": result.vm_id,
                    "template": result.template,
                    "memory_mb": result.memory_mb,
                    "cpus": result.cpus,
                    "ip_address": result.ip_address,
                    "ssh_port": result.ssh_port
                }
            
            return None
    
    def provision_vm(self, template: str) -> str:
        """Provision a new VM for the pool."""
        vm_name = f"pool_vm_{uuid4().hex[:8]}"
        
        vm_config = {
            "template": template,
            "memory_mb": self.default_vm_config["memory_mb"],
            "cpus": self.default_vm_config["cpus"],
            "disk_gb": self.default_vm_config["disk_gb"]
        }
        
        # Create VM record
        with SessionLocal() as db:
            vm_instance = VMInstance(
                name=vm_name,
                template=template,
                memory_mb=vm_config["memory_mb"],
                cpus=vm_config["cpus"],
                disk_gb=vm_config["disk_gb"],
                status=VMStatus.CREATING
            )
            db.add(vm_instance)
            db.commit()
            db.refresh(vm_instance)
            
            vm_id = str(vm_instance.id)
        
        # Queue VM creation task
        create_vm.delay(vm_id, "docker", vm_config)
        
        logger.info(f"Provisioned new VM for pool: {vm_name} ({vm_id})")
        return vm_id


# Global pool manager instance
pool_manager = VMPoolManager({
    "min_pool_size": 2,
    "max_pool_size": 10,
    "templates": ["ubuntu:20.04", "ubuntu:22.04", "alpine:latest"],
    "default_vm_config": {
        "memory_mb": 1024,
        "cpus": 1,
        "disk_gb": 20
    }
})


@shared_task(bind=True, name="services.workers.tasks.vm_pool_manager.maintain_pool")
def maintain_pool(self) -> Dict[str, Any]:
    """Maintain the VM pool by ensuring minimum VMs are available."""
    try:
        logger.info("Starting VM pool maintenance")
        
        status = pool_manager.get_pool_status()
        logger.info(f"Pool status: {status}")
        
        if status["needs_provisioning"]:
            needed = pool_manager.min_pool_size - status["ready"]
            logger.info(f"Need to provision {needed} VMs")
            
            provisioned = []
            for i in range(needed):
                # Rotate through templates
                template = pool_manager.vm_templates[i % len(pool_manager.vm_templates)]
                vm_id = pool_manager.provision_vm(template)
                provisioned.append(vm_id)
            
            return {
                "action": "provision",
                "provisioned_vms": provisioned,
                "pool_status": status
            }
        else:
            logger.info("Pool is adequately provisioned")
            return {
                "action": "none",
                "message": "Pool is adequately provisioned",
                "pool_status": status
            }
    
    except Exception as e:
        logger.error(f"Pool maintenance failed: {e}")
        raise


@shared_task(bind=True, name="services.workers.tasks.vm_pool_manager.cleanup_terminated_vms")
def cleanup_terminated_vms(self) -> Dict[str, Any]:
    """Clean up terminated VMs and trigger pool maintenance."""
    try:
        logger.info("Cleaning up terminated VMs")
        
        with SessionLocal() as db:
            # Find VMs that have been terminated for more than 5 minutes
            cutoff_time = datetime.utcnow() - timedelta(minutes=5)
            
            terminated_vms = db.execute(
                text("""
                    SELECT id, name, vm_id FROM vm_instances 
                    WHERE status = :status AND terminated_at < :cutoff_time
                """),
                {"status": VMStatus.TERMINATED.value, "cutoff_time": cutoff_time}
            ).fetchall()
            
            cleaned_up = []
            for vm in terminated_vms:
                # Remove from database
                db.execute(
                    text("DELETE FROM vm_instances WHERE id = :vm_id"),
                    {"vm_id": vm.id}
                )
                cleaned_up.append(str(vm.id))
                logger.info(f"Cleaned up terminated VM: {vm.name}")
            
            db.commit()
        
        # Trigger pool maintenance after cleanup
        if cleaned_up:
            maintain_pool.delay()
        
        return {
            "cleaned_up_count": len(cleaned_up),
            "cleaned_up_vms": cleaned_up
        }
    
    except Exception as e:
        logger.error(f"VM cleanup failed: {e}")
        raise


@shared_task(bind=True, name="services.workers.tasks.vm_pool_manager.get_vm_for_processing")
def get_vm_for_processing(self, template: Optional[str] = None) -> Dict[str, Any]:
    """Get a VM from the pool for file processing."""
    try:
        logger.info(f"Getting VM for processing (template: {template})")
        
        vm = pool_manager.get_ready_vm(template)
        
        if vm:
            logger.info(f"Allocated VM for processing: {vm['name']}")
            
            # Trigger pool maintenance to replace the allocated VM
            maintain_pool.delay()
            
            return {
                "success": True,
                "vm": vm
            }
        else:
            logger.warning("No ready VMs available in pool")
            
            # Try to provision a new VM immediately
            if template:
                vm_id = pool_manager.provision_vm(template)
            else:
                vm_id = pool_manager.provision_vm(pool_manager.vm_templates[0])
            
            return {
                "success": False,
                "message": "No ready VMs available, provisioning new VM",
                "provisioning_vm_id": vm_id
            }
    
    except Exception as e:
        logger.error(f"Failed to get VM for processing: {e}")
        raise
