{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  name = "turdparty-dev-shell";
  
  buildInputs = with pkgs; [
    # Shell and terminal tools
    zsh
    starship  # Modern shell prompt
    
    # Core development tools
    git
    git-lfs
    gh  # GitHub CLI
    
    # Python development
    python312
    python312Packages.pip
    python312Packages.virtualenv
    poetry
    
    # Node.js for frontend
    nodejs_20
    yarn
    
    # Database tools
    postgresql_15
    redis
    
    # Container and orchestration
    docker
    docker-compose
    
    # Testing and quality tools
    ruff
    mypy
    bandit

    # Pre-commit hooks
    pre-commit

    # Core application dependencies
    python312Packages.fastapi
    python312Packages.uvicorn
    python312Packages.pydantic
    python312Packages.httpx
    python312Packages.aiofiles
    python312Packages.python-multipart
    python312Packages.minio
    python312Packages.elasticsearch
    python312Packages.redis
    python312Packages.celery
    python312Packages.sqlalchemy
    python312Packages.alembic
    python312Packages.psycopg2

    # Advanced testing tools
    python312Packages.pytest
    python312Packages.pytest-asyncio
    python312Packages.pytest-cov
    python312Packages.pytest-html
    python312Packages.pytest-benchmark
    python312Packages.hypothesis
    python312Packages.factory-boy
    python312Packages.faker
    python312Packages.pytest-xdist
    python312Packages.pytest-mock
    python312Packages.pytest-timeout

    # Integration test dependencies
    python312Packages.httpx
    python312Packages.blake3
    python312Packages.minio
    python312Packages.elasticsearch
    python312Packages.python-dotenv
    python312Packages.grpcio
    python312Packages.grpcio-tools

    # VM monitoring dependencies
    python312Packages.psutil
    python312Packages.docker
    python312Packages.websockets

    # CLI dependencies for sexy TurdParty CLI
    python312Packages.click
    python312Packages.rich
    python312Packages.requests
    
    # Performance and monitoring
    htop
    iotop
    nethogs
    
    # File and text processing
    jq
    yq-go
    ripgrep
    fd
    bat
    eza  # Modern replacement for exa
    
    # Network tools
    curl
    wget
    httpie
    
    # Archive and compression
    unzip
    zip
    gzip
    gnutar
    
    # Development utilities
    fzf  # Fuzzy finder
    mc   # Midnight commander
    tree
    watch
    tmux
    
    # Security tools
    gnupg
    openssh
    
    # Build tools
    gnumake
    gcc
    
    # Documentation
    pandoc
    
    # Linting and formatting
    shellcheck
    shfmt
    
    # Monitoring and debugging
    strace
    lsof
    tcpdump
    
    # Math and calculations
    bc
    
    # Text editors (backup options)
    vim
    nano
  ];

  shellHook = ''
    echo "🚀 TurdParty Development Environment (Modern Testing Stack)"
    echo "📁 Project: $(basename $(pwd))"
    echo "🐍 Python: $(python --version)"
    echo "🐚 Shell: zsh with git branch display"
    echo ""

    # Configure zsh with git branch in prompt
    export SHELL="${pkgs.zsh}/bin/zsh"

    # Set up custom zsh configuration with git branch
    export ZDOTDIR="$PWD/.nix-zsh"
    mkdir -p "$ZDOTDIR"

    # Create zsh configuration with git branch prompt
    cat > "$ZDOTDIR/.zshrc" << 'EOF'
# Enable git prompt
autoload -Uz vcs_info
precmd() { vcs_info }
zstyle ':vcs_info:git:*' formats ' (%b)'
setopt PROMPT_SUBST

# Custom prompt with git branch
PROMPT='💩🎉 %F{cyan}%n@%m%f:%F{yellow}%~%f%F{green}''${vcs_info_msg_0_}%f %F{magenta}$%f '

# Enable command completion
autoload -U compinit
compinit

# History configuration
HISTFILE="$ZDOTDIR/.zsh_history"
HISTSIZE=10000
SAVEHIST=10000
setopt SHARE_HISTORY
setopt HIST_IGNORE_DUPS
setopt HIST_IGNORE_ALL_DUPS

# Enable useful zsh features
setopt AUTO_CD
setopt CORRECT
setopt EXTENDED_GLOB

# Aliases for TurdParty development
alias ll='eza -la --git'
alias la='eza -la'
alias ls='eza'
alias cat='bat'
alias grep='rg'
alias find='fd'
alias ps='ps aux'
alias df='df -h'
alias du='du -h'
alias free='free -h'

# Git aliases
alias gs='git status'
alias ga='git add'
alias gc='git commit'
alias gp='git push'
alias gl='git log --oneline'
alias gb='git branch'
alias gco='git checkout'

# TurdParty specific aliases
alias tp-status='./scripts/turdparty status'
alias tp-monitor='./scripts/turdparty monitor'
alias tp-start='./scripts/turdparty start'
alias tp-test='./scripts/run-parallel-tests.sh'
alias tp-logs='docker-compose logs -f'

# Docker aliases
alias dc='docker-compose'
alias dcu='docker-compose up -d'
alias dcd='docker-compose down'
alias dcl='docker-compose logs -f'
alias dps='docker ps'
alias di='docker images'

echo "🎉 Zsh configured with git branch display and TurdParty aliases!"
EOF

    # Start zsh if we're not already in it
    if [ "$SHELL" != "${pkgs.zsh}/bin/zsh" ]; then
      exec ${pkgs.zsh}/bin/zsh
    fi

    # Set up Python environment
    export PYTHONPATH="$PWD:$PYTHONPATH"

    # Set up development environment variables
    export DEVELOPMENT=true
    export DEBUG=true
    export LOG_LEVEL=DEBUG
    export TURDPARTY_ENV=development
    export COMPOSE_PROJECT_NAME=turdpartycollab
    export DOCKER_BUILDKIT=1
    export COVERAGE_THRESHOLD=80
    export PARALLEL_WORKERS=4

    # Create necessary directories
    mkdir -p logs data/uploads data/temp_files test-results coverage-archive reports

    echo "🔧 Modern Testing Commands:"
    echo "  python -m pytest tests/unit/ -v           # Unit tests"
    echo "  python -m pytest tests/property/ -v       # Property-based tests"
    echo "  python -m pytest tests/performance/ --benchmark-only  # Benchmarks"
    echo "  ruff check . && ruff format .              # Linting & formatting"
    echo "  pre-commit run --all-files                 # Pre-commit hooks"
    echo "  tox                                        # Multi-environment testing"
    echo ""
    echo "💩🎉 TurdParty CLI Commands:"
    echo "  ./scripts/turdparty status                 # Beautiful service dashboard"
    echo "  ./scripts/turdparty monitor                # Real-time monitoring (like BTOP)"
    echo "  ./scripts/turdparty start --logs           # Start services with logs"
    echo "  ./scripts/turdparty check                  # Dependency validation"
    echo ""
    echo "📚 Use: ./scripts/test_runner.sh help for full options"
    echo ""
  '';

  # Environment variables
  NIX_SHELL_PRESERVE_PROMPT = "1";
  
  # Shell configuration
  SHELL = "${pkgs.zsh}/bin/zsh";
  
  # Development tools configuration
  EDITOR = "vim";
  PAGER = "bat";
  
  # Python configuration
  PYTHONDONTWRITEBYTECODE = "1";
  PYTHONUNBUFFERED = "1";
  
  # Node.js configuration
  NODE_ENV = "development";
  
  # Git configuration
  GIT_EDITOR = "vim";
}
