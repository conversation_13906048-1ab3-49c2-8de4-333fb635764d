"""
Infrastructure Readiness Test
Quick test to verify all components are ready for Fibratus integration testing
"""

import pytest
import requests
import subprocess
import time


@pytest.mark.integration
class TestInfrastructureReady:
    """Test that infrastructure is ready for real Fibratus testing."""
    
    def test_api_health(self):
        """Test API is healthy and responding."""
        response = requests.get("http://localhost:8000/api/v1/health", timeout=10)
        assert response.status_code == 200
        
        health_data = response.json()
        assert health_data.get("status") == "healthy"
        print("✅ API is healthy")
    
    def test_elasticsearch_health(self):
        """Test Elasticsearch is healthy and responding."""
        response = requests.get("http://localhost:9200/_cluster/health", timeout=10)
        assert response.status_code == 200
        
        health_data = response.json()
        assert health_data.get("status") in ["green", "yellow"]
        print("✅ Elasticsearch is healthy")
    
    def test_docker_containers_running(self):
        """Test required Docker containers are running."""
        result = subprocess.run(
            ["docker", "ps", "--format", "{{.Names}}"],
            capture_output=True, text=True
        )
        
        containers = result.stdout.strip().split('\n')
        
        required_containers = [
            "turdpartycollab_api",
            "turdpartycollab_worker_vm",
            "turdpartycollab_elasticsearch",
            "turdpartycollab_database"
        ]
        
        for container in required_containers:
            assert any(container in c for c in containers), f"Container {container} not running"
            print(f"✅ {container} is running")
    
    def test_api_endpoints_accessible(self):
        """Test key API endpoints are accessible."""
        endpoints = [
            "/api/v1/health",
            "/api/v1/vms/",
            "/api/v1/files/"
        ]
        
        for endpoint in endpoints:
            response = requests.get(f"http://localhost:8000{endpoint}", timeout=10)
            assert response.status_code in [200, 401], f"Endpoint {endpoint} not accessible"
            print(f"✅ {endpoint} is accessible")
    
    def test_elasticsearch_indices_accessible(self):
        """Test Elasticsearch indices are accessible."""
        response = requests.get("http://localhost:9200/_cat/indices", timeout=10)
        assert response.status_code == 200
        print("✅ Elasticsearch indices are accessible")
    
    def test_worker_logs_available(self):
        """Test worker container logs are available."""
        result = subprocess.run(
            ["docker", "logs", "turdpartycollab_worker_vm", "--tail", "5"],
            capture_output=True, text=True
        )
        
        # Should not fail (exit code 0) even if logs are empty
        assert result.returncode == 0, "Worker logs not accessible"
        print("✅ Worker logs are accessible")
    
    def test_ready_for_fibratus_testing(self):
        """Final verification that system is ready for Fibratus testing."""
        print("\n🎯 INFRASTRUCTURE READINESS SUMMARY:")
        print("=" * 50)
        print("✅ API: Healthy and responding")
        print("✅ Elasticsearch: Healthy and accessible")
        print("✅ Docker Containers: All required containers running")
        print("✅ API Endpoints: All endpoints accessible")
        print("✅ Worker Logs: Available for monitoring")
        print("=" * 50)
        print("🚀 READY FOR FIBRATUS INTEGRATION TESTING!")
        print("=" * 50)


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
